(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[106],{330:(e,t,n)=>{"use strict";n.d(t,{UC:()=>eN,YJ:()=>eD,In:()=>eP,q7:()=>e_,VF:()=>eF,p4:()=>ez,JU:()=>eL,ZL:()=>ej,bL:()=>eA,wn:()=>eW,PP:()=>eI,wv:()=>eH,l9:()=>eR,WT:()=>eT,LM:()=>eO});var r=n(2115),o=n(7650);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var a=n(5185),l=n(6081),s=n(6101),u=n(9708),c=n(5155),d=r.createContext(void 0),f=n(9178),p=n(2293),h=n(7900),m=n(1285),v=n(8795),g=n(4378),y=n(3655),b=n(9033),w=n(5845),x=n(2712),k=n(2564),S=n(8168),C=n(3795),E=[" ","Enter","ArrowUp","ArrowDown"],M=[" ","Enter"],A="Select",[R,T,P]=function(e){let t=e+"CollectionProvider",[n,o]=(0,l.A)(t),[i,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),a=r.useRef(new Map).current;return(0,c.jsx)(i,{scope:t,itemMap:a,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=a(f,n),i=(0,s.s)(t,o.collectionRef);return(0,c.jsx)(u.DX,{ref:i,children:r})});p.displayName=f;let h=e+"CollectionItemSlot",m="data-radix-collection-item",v=r.forwardRef((e,t)=>{let{scope:n,children:o,...i}=e,l=r.useRef(null),d=(0,s.s)(t,l),f=a(h,n);return r.useEffect(()=>(f.itemMap.set(l,{ref:l,...i}),()=>void f.itemMap.delete(l))),(0,c.jsx)(u.DX,{[m]:"",ref:d,children:o})});return v.displayName=h,[{Provider:d,Slot:p,ItemSlot:v},function(t){let n=a(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},o]}(A),[j,N]=(0,l.A)(A,[P,v.Bk]),O=(0,v.Bk)(),[D,L]=j(A),[_,z]=j(A),F=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:a,value:l,defaultValue:s,onValueChange:u,dir:f,name:p,autoComplete:h,disabled:g,required:y,form:b}=e,x=O(t),[k,S]=r.useState(null),[C,E]=r.useState(null),[M,A]=r.useState(!1),T=function(e){let t=r.useContext(d);return e||t||"ltr"}(f),[P=!1,j]=(0,w.i)({prop:o,defaultProp:i,onChange:a}),[N,L]=(0,w.i)({prop:l,defaultProp:s,onChange:u}),z=r.useRef(null),F=!k||b||!!k.closest("form"),[I,W]=r.useState(new Set),H=Array.from(I).map(e=>e.props.value).join(";");return(0,c.jsx)(v.bL,{...x,children:(0,c.jsxs)(D,{required:y,scope:t,trigger:k,onTriggerChange:S,valueNode:C,onValueNodeChange:E,valueNodeHasChildren:M,onValueNodeHasChildrenChange:A,contentId:(0,m.B)(),value:N,onValueChange:L,open:P,onOpenChange:j,dir:T,triggerPointerDownPosRef:z,disabled:g,children:[(0,c.jsx)(R.Provider,{scope:t,children:(0,c.jsx)(_,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{W(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{W(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),F?(0,c.jsxs)(eC,{"aria-hidden":!0,required:y,tabIndex:-1,name:p,autoComplete:h,value:N,onChange:e=>L(e.target.value),disabled:g,form:b,children:[void 0===N?(0,c.jsx)("option",{value:""}):null,Array.from(I)]},H):null]})})};F.displayName=A;var I="SelectTrigger",W=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,l=O(n),u=L(I,n),d=u.disabled||o,f=(0,s.s)(t,u.onTriggerChange),p=T(n),h=r.useRef("touch"),[m,g,b]=eE(e=>{let t=p().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=eM(t,e,n);void 0!==r&&u.onValueChange(r.value)}),w=e=>{d||(u.onOpenChange(!0),b()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,c.jsx)(v.Mz,{asChild:!0,...l,children:(0,c.jsx)(y.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eS(u.value)?"":void 0,...i,ref:f,onClick:(0,a.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&w(e)}),onPointerDown:(0,a.m)(i.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,a.m)(i.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||g(e.key),(!t||" "!==e.key)&&E.includes(e.key)&&(w(),e.preventDefault())})})})});W.displayName=I;var H="SelectValue",B=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:a="",...l}=e,u=L(H,n),{onValueNodeHasChildrenChange:d}=u,f=void 0!==i,p=(0,s.s)(t,u.onValueNodeChange);return(0,x.N)(()=>{d(f)},[d,f]),(0,c.jsx)(y.sG.span,{...l,ref:p,style:{pointerEvents:"none"},children:eS(u.value)?(0,c.jsx)(c.Fragment,{children:a}):i})});B.displayName=H;var q=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,c.jsx)(y.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});q.displayName="SelectIcon";var G=e=>(0,c.jsx)(g.Z,{asChild:!0,...e});G.displayName="SelectPortal";var V="SelectContent",Y=r.forwardRef((e,t)=>{let n=L(V,e.__scopeSelect),[i,a]=r.useState();return((0,x.N)(()=>{a(new DocumentFragment)},[]),n.open)?(0,c.jsx)($,{...e,ref:t}):i?o.createPortal((0,c.jsx)(U,{scope:e.__scopeSelect,children:(0,c.jsx)(R.Slot,{scope:e.__scopeSelect,children:(0,c.jsx)("div",{children:e.children})})}),i):null});Y.displayName=V;var[U,X]=j(V),$=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:l,onPointerDownOutside:d,side:m,sideOffset:v,align:g,alignOffset:y,arrowPadding:b,collisionBoundary:w,collisionPadding:x,sticky:k,hideWhenDetached:E,avoidCollisions:M,...A}=e,R=L(V,n),[P,j]=r.useState(null),[N,O]=r.useState(null),D=(0,s.s)(t,e=>j(e)),[_,z]=r.useState(null),[F,I]=r.useState(null),W=T(n),[H,B]=r.useState(!1),q=r.useRef(!1);r.useEffect(()=>{if(P)return(0,S.Eq)(P)},[P]),(0,p.Oh)();let G=r.useCallback(e=>{let[t,...n]=W().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&N&&(N.scrollTop=0),n===r&&N&&(N.scrollTop=N.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[W,N]),Y=r.useCallback(()=>G([_,P]),[G,_,P]);r.useEffect(()=>{H&&Y()},[H,Y]);let{onOpenChange:X,triggerPointerDownPosRef:$}=R;r.useEffect(()=>{if(P){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!==(o=null===(n=$.current)||void 0===n?void 0:n.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(t.pageY)-(null!==(i=null===(r=$.current)||void 0===r?void 0:r.y)&&void 0!==i?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():P.contains(n.target)||X(!1),document.removeEventListener("pointermove",t),$.current=null};return null!==$.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[P,X,$]),r.useEffect(()=>{let e=()=>X(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[X]);let[Z,J]=eE(e=>{let t=W().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eM(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),ee=r.useCallback((e,t,n)=>{let r=!q.current&&!n;(void 0!==R.value&&R.value===t||r)&&(z(e),r&&(q.current=!0))},[R.value]),et=r.useCallback(()=>null==P?void 0:P.focus(),[P]),en=r.useCallback((e,t,n)=>{let r=!q.current&&!n;(void 0!==R.value&&R.value===t||r)&&I(e)},[R.value]),er="popper"===o?Q:K,eo=er===Q?{side:m,sideOffset:v,align:g,alignOffset:y,arrowPadding:b,collisionBoundary:w,collisionPadding:x,sticky:k,hideWhenDetached:E,avoidCollisions:M}:{};return(0,c.jsx)(U,{scope:n,content:P,viewport:N,onViewportChange:O,itemRefCallback:ee,selectedItem:_,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:Y,selectedItemText:F,position:o,isPositioned:H,searchRef:Z,children:(0,c.jsx)(C.A,{as:u.DX,allowPinchZoom:!0,children:(0,c.jsx)(h.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(i,e=>{var t;null===(t=R.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,c.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,c.jsx)(er,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...A,...eo,onPlaced:()=>B(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...A.style},onKeyDown:(0,a.m)(A.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||J(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=W().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>G(t)),e.preventDefault()}})})})})})})});$.displayName="SelectContentImpl";var K=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...a}=e,l=L(V,n),u=X(V,n),[d,f]=r.useState(null),[p,h]=r.useState(null),m=(0,s.s)(t,e=>h(e)),v=T(n),g=r.useRef(!1),b=r.useRef(!0),{viewport:w,selectedItem:k,selectedItemText:S,focusSelectedItem:C}=u,E=r.useCallback(()=>{if(l.trigger&&l.valueNode&&d&&p&&w&&k&&S){let e=l.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),n=l.valueNode.getBoundingClientRect(),r=S.getBoundingClientRect();if("rtl"!==l.dir){let o=r.left-t.left,a=n.left-o,l=e.left-a,s=e.width+l,u=Math.max(s,t.width),c=i(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.left=c+"px"}else{let o=t.right-r.right,a=window.innerWidth-n.right-o,l=window.innerWidth-e.right-a,s=e.width+l,u=Math.max(s,t.width),c=i(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.right=c+"px"}let a=v(),s=window.innerHeight-20,u=w.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),h=parseInt(c.paddingTop,10),m=parseInt(c.borderBottomWidth,10),y=f+h+u+parseInt(c.paddingBottom,10)+m,b=Math.min(5*k.offsetHeight,y),x=window.getComputedStyle(w),C=parseInt(x.paddingTop,10),E=parseInt(x.paddingBottom,10),M=e.top+e.height/2-10,A=k.offsetHeight/2,R=f+h+(k.offsetTop+A);if(R<=M){let e=a.length>0&&k===a[a.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-M,A+(e?E:0)+(p.clientHeight-w.offsetTop-w.offsetHeight)+m);d.style.height=R+t+"px"}else{let e=a.length>0&&k===a[0].ref.current;d.style.top="0px";let t=Math.max(M,f+w.offsetTop+(e?C:0)+A);d.style.height=t+(y-R)+"px",w.scrollTop=R-M+w.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=b+"px",d.style.maxHeight=s+"px",null==o||o(),requestAnimationFrame(()=>g.current=!0)}},[v,l.trigger,l.valueNode,d,p,w,k,S,l.dir,o]);(0,x.N)(()=>E(),[E]);let[M,A]=r.useState();(0,x.N)(()=>{p&&A(window.getComputedStyle(p).zIndex)},[p]);let R=r.useCallback(e=>{e&&!0===b.current&&(E(),null==C||C(),b.current=!1)},[E,C]);return(0,c.jsx)(Z,{scope:n,contentWrapper:d,shouldExpandOnScrollRef:g,onScrollButtonChange:R,children:(0,c.jsx)("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:M},children:(0,c.jsx)(y.sG.div,{...a,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});K.displayName="SelectItemAlignedPosition";var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,a=O(n);return(0,c.jsx)(v.UC,{...a,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Q.displayName="SelectPopperPosition";var[Z,J]=j(V,{}),ee="SelectViewport",et=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,l=X(ee,n),u=J(ee,n),d=(0,s.s)(t,l.onViewportChange),f=r.useRef(0);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,c.jsx)(R.Slot,{scope:n,children:(0,c.jsx)(y.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,a.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if((null==r?void 0:r.current)&&n){let e=Math.abs(f.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,a=Math.min(r,i),l=i-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=l>0?l:0,n.style.justifyContent="flex-end")}}}f.current=t.scrollTop})})})]})});et.displayName=ee;var en="SelectGroup",[er,eo]=j(en),ei=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,m.B)();return(0,c.jsx)(er,{scope:n,id:o,children:(0,c.jsx)(y.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});ei.displayName=en;var ea="SelectLabel",el=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=eo(ea,n);return(0,c.jsx)(y.sG.div,{id:o.id,...r,ref:t})});el.displayName=ea;var es="SelectItem",[eu,ec]=j(es),ed=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:l,...u}=e,d=L(es,n),f=X(es,n),p=d.value===o,[h,v]=r.useState(null!=l?l:""),[g,b]=r.useState(!1),w=(0,s.s)(t,e=>{var t;return null===(t=f.itemRefCallback)||void 0===t?void 0:t.call(f,e,o,i)}),x=(0,m.B)(),k=r.useRef("touch"),S=()=>{i||(d.onValueChange(o),d.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,c.jsx)(eu,{scope:n,value:o,disabled:i,textId:x,isSelected:p,onItemTextChange:r.useCallback(e=>{v(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,c.jsx)(R.ItemSlot,{scope:n,value:o,disabled:i,textValue:h,children:(0,c.jsx)(y.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":g?"":void 0,"aria-selected":p&&g,"data-state":p?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...u,ref:w,onFocus:(0,a.m)(u.onFocus,()=>b(!0)),onBlur:(0,a.m)(u.onBlur,()=>b(!1)),onClick:(0,a.m)(u.onClick,()=>{"mouse"!==k.current&&S()}),onPointerUp:(0,a.m)(u.onPointerUp,()=>{"mouse"===k.current&&S()}),onPointerDown:(0,a.m)(u.onPointerDown,e=>{k.current=e.pointerType}),onPointerMove:(0,a.m)(u.onPointerMove,e=>{if(k.current=e.pointerType,i){var t;null===(t=f.onItemLeave)||void 0===t||t.call(f)}else"mouse"===k.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=f.onItemLeave)||void 0===t||t.call(f)}}),onKeyDown:(0,a.m)(u.onKeyDown,e=>{var t;((null===(t=f.searchRef)||void 0===t?void 0:t.current)===""||" "!==e.key)&&(M.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ed.displayName=es;var ef="SelectItemText",ep=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:a,...l}=e,u=L(ef,n),d=X(ef,n),f=ec(ef,n),p=z(ef,n),[h,m]=r.useState(null),v=(0,s.s)(t,e=>m(e),f.onItemTextChange,e=>{var t;return null===(t=d.itemTextRefCallback)||void 0===t?void 0:t.call(d,e,f.value,f.disabled)}),g=null==h?void 0:h.textContent,b=r.useMemo(()=>(0,c.jsx)("option",{value:f.value,disabled:f.disabled,children:g},f.value),[f.disabled,f.value,g]),{onNativeOptionAdd:w,onNativeOptionRemove:k}=p;return(0,x.N)(()=>(w(b),()=>k(b)),[w,k,b]),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(y.sG.span,{id:f.textId,...l,ref:v}),f.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(l.children,u.valueNode):null]})});ep.displayName=ef;var eh="SelectItemIndicator",em=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ec(eh,n).isSelected?(0,c.jsx)(y.sG.span,{"aria-hidden":!0,...r,ref:t}):null});em.displayName=eh;var ev="SelectScrollUpButton",eg=r.forwardRef((e,t)=>{let n=X(ev,e.__scopeSelect),o=J(ev,e.__scopeSelect),[i,a]=r.useState(!1),l=(0,s.s)(t,o.onScrollButtonChange);return(0,x.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){a(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,c.jsx)(ew,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eg.displayName=ev;var ey="SelectScrollDownButton",eb=r.forwardRef((e,t)=>{let n=X(ey,e.__scopeSelect),o=J(ey,e.__scopeSelect),[i,a]=r.useState(!1),l=(0,s.s)(t,o.onScrollButtonChange);return(0,x.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,c.jsx)(ew,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eb.displayName=ey;var ew=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,l=X("SelectScrollButton",n),s=r.useRef(null),u=T(n),d=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>d(),[d]),(0,x.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[u]),(0,c.jsx)(y.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,a.m)(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,a.m)(i.onPointerMove,()=>{var e;null===(e=l.onItemLeave)||void 0===e||e.call(l),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,a.m)(i.onPointerLeave,()=>{d()})})}),ex=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,c.jsx)(y.sG.div,{"aria-hidden":!0,...r,ref:t})});ex.displayName="SelectSeparator";var ek="SelectArrow";function eS(e){return""===e||void 0===e}r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=O(n),i=L(ek,n),a=X(ek,n);return i.open&&"popper"===a.position?(0,c.jsx)(v.i3,{...o,...r,ref:t}):null}).displayName=ek;var eC=r.forwardRef((e,t)=>{let{value:n,...o}=e,i=r.useRef(null),a=(0,s.s)(t,i),l=function(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(n);return r.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(l!==n&&t){let r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[l,n]),(0,c.jsx)(k.s,{asChild:!0,children:(0,c.jsx)("select",{...o,ref:a,defaultValue:n})})});function eE(e){let t=(0,b.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),a=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,a]}function eM(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1,l=(r=e,o=Math.max(a,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(l=l.filter(e=>e!==n));let s=l.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==n?s:void 0}eC.displayName="BubbleSelect";var eA=F,eR=W,eT=B,eP=q,ej=G,eN=Y,eO=et,eD=ei,eL=el,e_=ed,ez=ep,eF=em,eI=eg,eW=eb,eH=ex},381:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},537:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Battery",[["rect",{width:"16",height:"10",x:"2",y:"7",rx:"2",ry:"2",key:"1w10f2"}],["line",{x1:"22",x2:"22",y1:"11",y2:"13",key:"4dh1rd"}]])},646:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},968:(e,t,n)=>{"use strict";n.d(t,{b:()=>l});var r=n(2115),o=n(3655),i=n(5155),a=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var l=a},1007:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1243:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1284:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},1285:(e,t,n)=>{"use strict";n.d(t,{B:()=>s});var r,o=n(2115),i=n(2712),a=(r||(r=n.t(o,2)))["useId".toString()]||(()=>void 0),l=0;function s(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},1539:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},1788:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},1981:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},2085:(e,t,n)=>{"use strict";n.d(t,{F:()=>a});var r=n(2596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.$,a=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:a,defaultVariants:l}=t,s=Object.keys(a).map(e=>{let t=null==n?void 0:n[e],r=null==l?void 0:l[e];if(null===t)return null;let i=o(t)||o(r);return a[e][i]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return i(e,s,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...l,...u}[t]):({...l,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},2138:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2269:(e,t,n)=>{"use strict";var r=n(9509);n(8375);var o=n(2115),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(o),a=void 0!==r&&r.env&&!0,l=function(e){return"[object String]"===Object.prototype.toString.call(e)},s=function(){function e(e){var t=void 0===e?{}:e,n=t.name,r=void 0===n?"stylesheet":n,o=t.optimizeForSpeed,i=void 0===o?a:o;u(l(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",u("boolean"==typeof i,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=i,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var s="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=s?s.getAttribute("content"):null}var t=e.prototype;return t.setOptimizeForSpeed=function(e){u("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),u(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;if(u(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(a||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,n){return"number"==typeof n?e._serverSheet.cssRules[n]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),n},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,t){if(u(l(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var n=this.getSheet();"number"!=typeof t&&(t=n.cssRules.length);try{n.insertRule(e,t)}catch(t){return a||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},t.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var n="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!n.cssRules[e])return e;n.deleteRule(e);try{n.insertRule(t,e)}catch(r){a||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),n.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];u(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},t.deleteRule=function(e){if("undefined"==typeof window){this._serverSheet.deleteRule(e);return}if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];u(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},t.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},t.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,n){return n?t=t.concat(Array.prototype.map.call(e.getSheetForTag(n).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},t.makeStyleTag=function(e,t,n){t&&u(l(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var o=document.head||document.getElementsByTagName("head")[0];return n?o.insertBefore(r,n):o.appendChild(r),r},function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function u(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,n=e.length;n;)t=33*t^e.charCodeAt(--n);return t>>>0},d={};function f(e,t){if(!t)return"jsx-"+e;var n=String(t),r=e+n;return d[r]||(d[r]="jsx-"+c(e+"-"+n)),d[r]}function p(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var n=e+t;return d[n]||(d[n]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[n]}var h=function(){function e(e){var t=void 0===e?{}:e,n=t.styleSheet,r=void 0===n?null:n,o=t.optimizeForSpeed,i=void 0!==o&&o;this._sheet=r||new s({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),r&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var n=this.getIdAndRules(e),r=n.styleId,o=n.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var i=o.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=i,this._instancesCounts[r]=1},t.remove=function(e){var t=this,n=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(n in this._instancesCounts,"styleId: `"+n+"` not found"),this._instancesCounts[n]-=1,this._instancesCounts[n]<1){var r=this._fromServer&&this._fromServer[n];r?(r.parentNode.removeChild(r),delete this._fromServer[n]):(this._indices[n].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[n]),delete this._instancesCounts[n]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],n=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return n[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,n;return t=this.cssRules(),void 0===(n=e)&&(n={}),t.map(function(e){var t=e[0],r=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:n.nonce?n.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,n=e.dynamic,r=e.id;if(n){var o=f(r,n);return{styleId:o,rules:Array.isArray(t)?t.map(function(e){return p(o,e)}):[p(o,t)]}}return{styleId:f(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=o.createContext(null);m.displayName="StyleSheetContext";var v=i.default.useInsertionEffect||i.default.useLayoutEffect,g="undefined"!=typeof window?new h:void 0;function y(e){var t=g||o.useContext(m);return t&&("undefined"==typeof window?t.add(e):v(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}y.dynamic=function(e){return e.map(function(e){return f(e[0],e[1])}).join(" ")},t.style=y},2293:(e,t,n)=>{"use strict";n.d(t,{Oh:()=>i});var r=n(2115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:a()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2355:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},2432:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},2525:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2564:(e,t,n)=>{"use strict";n.d(t,{b:()=>l,s:()=>a});var r=n(2115),o=n(3655),i=n(5155),a=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));a.displayName="VisuallyHidden";var l=a},2596:(e,t,n)=>{"use strict";function r(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=function e(t){var n,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var i=t.length;for(n=0;n<i;n++)t[n]&&(r=e(t[n]))&&(o&&(o+=" "),o+=r)}else for(r in t)t[r]&&(o&&(o+=" "),o+=r)}return o}(e))&&(r&&(r+=" "),r+=t);return r}n.d(t,{$:()=>r})},2657:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2712:(e,t,n)=>{"use strict";n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},2713:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3052:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3319:(e,t,n)=>{"use strict";n.d(t,{GP:()=>F});let r={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function o(e){return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}let i={date:o({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:o({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:o({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},a={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function l(e){return(t,n)=>{let r;if("formatting"===((null==n?void 0:n.context)?String(n.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,o=(null==n?void 0:n.width)?String(n.width):t;r=e.formattingValues[o]||e.formattingValues[t]}else{let t=e.defaultWidth,o=(null==n?void 0:n.width)?String(n.width):e.defaultWidth;r=e.values[o]||e.values[t]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function s(e){return function(t){let n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=r.width,i=o&&e.matchPatterns[o]||e.matchPatterns[e.defaultMatchWidth],a=t.match(i);if(!a)return null;let l=a[0],s=o&&e.parsePatterns[o]||e.parsePatterns[e.defaultParseWidth],u=Array.isArray(s)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}(s,e=>e.test(l)):function(e,t){for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}(s,e=>e.test(l));return n=e.valueCallback?e.valueCallback(u):u,{value:n=r.valueCallback?r.valueCallback(n):n,rest:t.slice(l.length)}}}let u={code:"en-US",formatDistance:(e,t,n)=>{let o;let i=r[e];return(o="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",t.toString()),null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"in "+o:o+" ago":o},formatLong:i,formatRelative:(e,t,n,r)=>a[e],localize:{ordinalNumber:(e,t)=>{let n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:l({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:l({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:l({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:l({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:l({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.match(e.matchPattern);if(!r)return null;let o=r[0],i=t.match(e.parsePattern);if(!i)return null;let a=e.valueCallback?e.valueCallback(i[0]):i[0];return{value:a=n.valueCallback?n.valueCallback(a):a,rest:t.slice(o.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:s({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:s({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:s({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:s({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:s({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},c={},d=Symbol.for("constructDateFrom");function f(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&d in e?e[d](t):e instanceof Date?new e.constructor(t):new Date(t)}function p(e,t){return f(t||e,e)}function h(e){let t=p(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function m(e,t){let n=p(e,null==t?void 0:t.in);return n.setHours(0,0,0,0),n}function v(e,t){var n,r,o,i,a,l,s,u;let d=null!==(u=null!==(s=null!==(l=null!==(a=null==t?void 0:t.weekStartsOn)&&void 0!==a?a:null==t?void 0:null===(r=t.locale)||void 0===r?void 0:null===(n=r.options)||void 0===n?void 0:n.weekStartsOn)&&void 0!==l?l:c.weekStartsOn)&&void 0!==s?s:null===(i=c.locale)||void 0===i?void 0:null===(o=i.options)||void 0===o?void 0:o.weekStartsOn)&&void 0!==u?u:0,f=p(e,null==t?void 0:t.in),h=f.getDay();return f.setDate(f.getDate()-(7*(h<d)+h-d)),f.setHours(0,0,0,0),f}function g(e,t){return v(e,{...t,weekStartsOn:1})}function y(e,t){let n=p(e,null==t?void 0:t.in),r=n.getFullYear(),o=f(n,0);o.setFullYear(r+1,0,4),o.setHours(0,0,0,0);let i=g(o),a=f(n,0);a.setFullYear(r,0,4),a.setHours(0,0,0,0);let l=g(a);return n.getTime()>=i.getTime()?r+1:n.getTime()>=l.getTime()?r:r-1}function b(e,t){var n,r,o,i,a,l,s,u;let d=p(e,null==t?void 0:t.in),h=d.getFullYear(),m=null!==(u=null!==(s=null!==(l=null!==(a=null==t?void 0:t.firstWeekContainsDate)&&void 0!==a?a:null==t?void 0:null===(r=t.locale)||void 0===r?void 0:null===(n=r.options)||void 0===n?void 0:n.firstWeekContainsDate)&&void 0!==l?l:c.firstWeekContainsDate)&&void 0!==s?s:null===(i=c.locale)||void 0===i?void 0:null===(o=i.options)||void 0===o?void 0:o.firstWeekContainsDate)&&void 0!==u?u:1,g=f((null==t?void 0:t.in)||e,0);g.setFullYear(h+1,0,m),g.setHours(0,0,0,0);let y=v(g,t),b=f((null==t?void 0:t.in)||e,0);b.setFullYear(h,0,m),b.setHours(0,0,0,0);let w=v(b,t);return+d>=+y?h+1:+d>=+w?h:h-1}function w(e,t){let n=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+n}let x={y(e,t){let n=e.getFullYear(),r=n>0?n:1-n;return w("yy"===t?r%100:r,t.length)},M(e,t){let n=e.getMonth();return"M"===t?String(n+1):w(n+1,2)},d:(e,t)=>w(e.getDate(),t.length),a(e,t){let n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(e,t)=>w(e.getHours()%12||12,t.length),H:(e,t)=>w(e.getHours(),t.length),m:(e,t)=>w(e.getMinutes(),t.length),s:(e,t)=>w(e.getSeconds(),t.length),S(e,t){let n=t.length;return w(Math.trunc(e.getMilliseconds()*Math.pow(10,n-3)),t.length)}},k={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},S={G:function(e,t,n){let r=+(e.getFullYear()>0);switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if("yo"===t){let t=e.getFullYear();return n.ordinalNumber(t>0?t:1-t,{unit:"year"})}return x.y(e,t)},Y:function(e,t,n,r){let o=b(e,r),i=o>0?o:1-o;return"YY"===t?w(i%100,2):"Yo"===t?n.ordinalNumber(i,{unit:"year"}):w(i,t.length)},R:function(e,t){return w(y(e),t.length)},u:function(e,t){return w(e.getFullYear(),t.length)},Q:function(e,t,n){let r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return w(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){let r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return w(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){let r=e.getMonth();switch(t){case"M":case"MM":return x.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){let r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return w(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){let o=function(e,t){let n=p(e,null==t?void 0:t.in);return Math.round((+v(n,t)-+function(e,t){var n,r,o,i,a,l,s,u;let d=null!==(u=null!==(s=null!==(l=null!==(a=null==t?void 0:t.firstWeekContainsDate)&&void 0!==a?a:null==t?void 0:null===(r=t.locale)||void 0===r?void 0:null===(n=r.options)||void 0===n?void 0:n.firstWeekContainsDate)&&void 0!==l?l:c.firstWeekContainsDate)&&void 0!==s?s:null===(i=c.locale)||void 0===i?void 0:null===(o=i.options)||void 0===o?void 0:o.firstWeekContainsDate)&&void 0!==u?u:1,p=b(e,t),h=f((null==t?void 0:t.in)||e,0);return h.setFullYear(p,0,d),h.setHours(0,0,0,0),v(h,t)}(n,t))/6048e5)+1}(e,r);return"wo"===t?n.ordinalNumber(o,{unit:"week"}):w(o,t.length)},I:function(e,t,n){let r=function(e,t){let n=p(e,void 0);return Math.round((+g(n)-+function(e,t){let n=y(e,void 0),r=f(e,0);return r.setFullYear(n,0,4),r.setHours(0,0,0,0),g(r)}(n))/6048e5)+1}(e);return"Io"===t?n.ordinalNumber(r,{unit:"week"}):w(r,t.length)},d:function(e,t,n){return"do"===t?n.ordinalNumber(e.getDate(),{unit:"date"}):x.d(e,t)},D:function(e,t,n){let r=function(e,t){let n=p(e,void 0);return function(e,t,n){let[r,o]=function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];let o=f.bind(null,e||n.find(e=>"object"==typeof e));return n.map(o)}(void 0,e,t),i=m(r),a=m(o);return Math.round((+i-h(i)-(+a-h(a)))/864e5)}(n,function(e,t){let n=p(e,void 0);return n.setFullYear(n.getFullYear(),0,1),n.setHours(0,0,0,0),n}(n))+1}(e);return"Do"===t?n.ordinalNumber(r,{unit:"dayOfYear"}):w(r,t.length)},E:function(e,t,n){let r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){let o=e.getDay(),i=(o-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(i);case"ee":return w(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(o,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(o,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(o,{width:"short",context:"formatting"});default:return n.day(o,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){let o=e.getDay(),i=(o-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(i);case"cc":return w(i,t.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(o,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(o,{width:"narrow",context:"standalone"});case"cccccc":return n.day(o,{width:"short",context:"standalone"});default:return n.day(o,{width:"wide",context:"standalone"})}},i:function(e,t,n){let r=e.getDay(),o=0===r?7:r;switch(t){case"i":return String(o);case"ii":return w(o,t.length);case"io":return n.ordinalNumber(o,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){let r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,t,n){let r;let o=e.getHours();switch(r=12===o?k.noon:0===o?k.midnight:o/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(e,t,n){let r;let o=e.getHours();switch(r=o>=17?k.evening:o>=12?k.afternoon:o>=4?k.morning:k.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(e,t,n){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),n.ordinalNumber(t,{unit:"hour"})}return x.h(e,t)},H:function(e,t,n){return"Ho"===t?n.ordinalNumber(e.getHours(),{unit:"hour"}):x.H(e,t)},K:function(e,t,n){let r=e.getHours()%12;return"Ko"===t?n.ordinalNumber(r,{unit:"hour"}):w(r,t.length)},k:function(e,t,n){let r=e.getHours();return(0===r&&(r=24),"ko"===t)?n.ordinalNumber(r,{unit:"hour"}):w(r,t.length)},m:function(e,t,n){return"mo"===t?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):x.m(e,t)},s:function(e,t,n){return"so"===t?n.ordinalNumber(e.getSeconds(),{unit:"second"}):x.s(e,t)},S:function(e,t){return x.S(e,t)},X:function(e,t,n){let r=e.getTimezoneOffset();if(0===r)return"Z";switch(t){case"X":return E(r);case"XXXX":case"XX":return M(r);default:return M(r,":")}},x:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"x":return E(r);case"xxxx":case"xx":return M(r);default:return M(r,":")}},O:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+C(r,":");default:return"GMT"+M(r,":")}},z:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+C(r,":");default:return"GMT"+M(r,":")}},t:function(e,t,n){return w(Math.trunc(+e/1e3),t.length)},T:function(e,t,n){return w(+e,t.length)}};function C(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=e>0?"-":"+",r=Math.abs(e),o=Math.trunc(r/60),i=r%60;return 0===i?n+String(o):n+String(o)+t+w(i,2)}function E(e,t){return e%60==0?(e>0?"-":"+")+w(Math.abs(e)/60,2):M(e,t)}function M(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=Math.abs(e);return(e>0?"-":"+")+w(Math.trunc(n/60),2)+t+w(n%60,2)}let A=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},R=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},T={p:R,P:(e,t)=>{let n;let r=e.match(/(P+)(p+)?/)||[],o=r[1],i=r[2];if(!i)return A(e,t);switch(o){case"P":n=t.dateTime({width:"short"});break;case"PP":n=t.dateTime({width:"medium"});break;case"PPP":n=t.dateTime({width:"long"});break;default:n=t.dateTime({width:"full"})}return n.replace("{{date}}",A(o,t)).replace("{{time}}",R(i,t))}},P=/^D+$/,j=/^Y+$/,N=["D","DD","YY","YYYY"],O=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,D=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,L=/^'([^]*?)'?$/,_=/''/g,z=/[a-zA-Z]/;function F(e,t,n){var r,o,i,a,l,s,d,f,h,m,v,g,y,b,w,x,k,C;let E=null!==(m=null!==(h=null==n?void 0:n.locale)&&void 0!==h?h:c.locale)&&void 0!==m?m:u,M=null!==(b=null!==(y=null!==(g=null!==(v=null==n?void 0:n.firstWeekContainsDate)&&void 0!==v?v:null==n?void 0:null===(o=n.locale)||void 0===o?void 0:null===(r=o.options)||void 0===r?void 0:r.firstWeekContainsDate)&&void 0!==g?g:c.firstWeekContainsDate)&&void 0!==y?y:null===(a=c.locale)||void 0===a?void 0:null===(i=a.options)||void 0===i?void 0:i.firstWeekContainsDate)&&void 0!==b?b:1,A=null!==(C=null!==(k=null!==(x=null!==(w=null==n?void 0:n.weekStartsOn)&&void 0!==w?w:null==n?void 0:null===(s=n.locale)||void 0===s?void 0:null===(l=s.options)||void 0===l?void 0:l.weekStartsOn)&&void 0!==x?x:c.weekStartsOn)&&void 0!==k?k:null===(f=c.locale)||void 0===f?void 0:null===(d=f.options)||void 0===d?void 0:d.weekStartsOn)&&void 0!==C?C:0,R=p(e,null==n?void 0:n.in);if(!(R instanceof Date||"object"==typeof R&&"[object Date]"===Object.prototype.toString.call(R))&&"number"!=typeof R||isNaN(+p(R)))throw RangeError("Invalid time value");let F=t.match(D).map(e=>{let t=e[0];return"p"===t||"P"===t?(0,T[t])(e,E.formatLong):e}).join("").match(O).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:function(e){let t=e.match(L);return t?t[1].replace(_,"'"):e}(e)};if(S[t])return{isToken:!0,value:e};if(t.match(z))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});E.localize.preprocessor&&(F=E.localize.preprocessor(R,F));let I={firstWeekContainsDate:M,weekStartsOn:A,locale:E};return F.map(r=>{if(!r.isToken)return r.value;let o=r.value;return(!(null==n?void 0:n.useAdditionalWeekYearTokens)&&j.test(o)||!(null==n?void 0:n.useAdditionalDayOfYearTokens)&&P.test(o))&&!function(e,t,n){let r=function(e,t,n){let r="Y"===e[0]?"years":"days of the month";return"Use `".concat(e.toLowerCase(),"` instead of `").concat(e,"` (in `").concat(t,"`) for formatting ").concat(r," to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md")}(e,t,n);if(console.warn(r),N.includes(e))throw RangeError(r)}(o,t,String(e)),(0,S[o[0]])(R,o,E.localize,I)}).join("")}},3655:(e,t,n)=>{"use strict";n.d(t,{hO:()=>s,sG:()=>l});var r=n(2115),o=n(7650),i=n(9708),a=n(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...o}=e,l=r?i.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(l,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},3717:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3795:(e,t,n)=>{"use strict";n.d(t,{A:()=>V});var r,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(2115)),l="right-scroll-bar-position",s="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,i,a=(t=null,void 0===n&&(n=f),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(o)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=o({async:!0,ssr:!1},e),a}(),h=function(){},m=a.forwardRef(function(e,t){var n,r,l,s,f=a.useRef(null),m=a.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=m[0],g=m[1],y=e.forwardProps,b=e.children,w=e.className,x=e.removeScrollBar,k=e.enabled,S=e.shards,C=e.sideCar,E=e.noRelative,M=e.noIsolation,A=e.inert,R=e.allowPinchZoom,T=e.as,P=e.gapMode,j=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),N=(n=[f,t],r=function(e){return n.forEach(function(t){return u(t,e)})},(l=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return l.value},set current(value){var e=l.value;e!==value&&(l.value=value,l.callback(value,e))}}}})[0]).callback=r,s=l.facade,c(function(){var e=d.get(s);if(e){var t=new Set(e),r=new Set(n),o=s.current;t.forEach(function(e){r.has(e)||u(e,null)}),r.forEach(function(e){t.has(e)||u(e,o)})}d.set(s,n)},[n]),s),O=o(o({},j),v);return a.createElement(a.Fragment,null,k&&a.createElement(C,{sideCar:p,removeScrollBar:x,shards:S,noRelative:E,noIsolation:M,inert:A,setCallbacks:g,allowPinchZoom:!!R,lockRef:f,gapMode:P}),y?a.cloneElement(a.Children.only(b),o(o({},O),{ref:N})):a.createElement(void 0===T?"div":T,o({},O,{className:w,ref:N}),b))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:s,zeroRight:l};var v=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,o({},n))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},k=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(n),x(r),x(o)]},S=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=k(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},C=b(),E="data-scroll-locked",M=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(E,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(E,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},A=function(){var e=parseInt(document.body.getAttribute(E)||"0",10);return isFinite(e)?e:0},R=function(){a.useEffect(function(){return document.body.setAttribute(E,(A()+1).toString()),function(){var e=A()-1;e<=0?document.body.removeAttribute(E):document.body.setAttribute(E,e.toString())}},[])},T=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;R();var i=a.useMemo(function(){return S(o)},[o]);return a.createElement(C,{styles:M(i,!t,o,n?"":"!important")})},P=!1;if("undefined"!=typeof window)try{var j=Object.defineProperty({},"passive",{get:function(){return P=!0,!0}});window.addEventListener("test",j,j),window.removeEventListener("test",j,j)}catch(e){P=!1}var N=!!P&&{passive:!1},O=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),L(e,r)){var o=_(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},L=function(e,t){return"v"===e?O(t,"overflowY"):O(t,"overflowX")},_=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},z=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,s=n.target,u=t.contains(s),c=!1,d=l>0,f=0,p=0;do{if(!s)break;var h=_(e,s),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&L(e,s)&&(f+=v,p+=m);var g=s.parentNode;s=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&s!==document.body||u&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&l>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(c=!0),c},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},I=function(e){return[e.deltaX,e.deltaY]},W=function(e){return e&&"current"in e?e.current:e},H=0,B=[];let q=(p.useMedium(function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(H++)[0],i=a.useState(b)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(W),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=F(e),a=n.current,s="deltaX"in e?e.deltaX:a[0]-i[0],u="deltaY"in e?e.deltaY:a[1]-i[1],c=e.target,d=Math.abs(s)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=D(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(s||u)&&(r.current=o),!o)return!0;var p=r.current||o;return z(p,t,e,"h"===p?s:u,!0)},[]),u=a.useCallback(function(e){if(B.length&&B[B.length-1]===i){var n="deltaY"in e?I(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(W).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=F(e),r.current=void 0},[]),f=a.useCallback(function(t){c(t.type,I(t),t.target,s(t,e.lockRef.current))},[]),p=a.useCallback(function(t){c(t.type,F(t),t.target,s(t,e.lockRef.current))},[]);a.useEffect(function(){return B.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,N),document.addEventListener("touchmove",u,N),document.addEventListener("touchstart",d,N),function(){B=B.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,N),document.removeEventListener("touchmove",u,N),document.removeEventListener("touchstart",d,N)}},[]);var h=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(T,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),v);var G=a.forwardRef(function(e,t){return a.createElement(m,o({},e,{ref:t,sideCar:q}))});G.classNames=m.classNames;let V=G},3861:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},3904:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4186:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4229:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4311:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},4378:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(2115),o=n(7650),i=n(3655),a=n(2712),l=n(5155),s=r.forwardRef((e,t)=>{var n,s;let{container:u,...c}=e,[d,f]=r.useState(!1);(0,a.N)(()=>f(!0),[]);let p=u||d&&(null===(s=globalThis)||void 0===s?void 0:null===(n=s.document)||void 0===n?void 0:n.body);return p?o.createPortal((0,l.jsx)(i.sG.div,{...c,ref:t}),p):null});s.displayName="Portal"},4416:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4516:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4616:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4835:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},5040:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5185:(e,t,n)=>{"use strict";function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}n.d(t,{m:()=>r})},5196:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5452:(e,t,n)=>{"use strict";n.d(t,{UC:()=>et,VY:()=>er,ZL:()=>J,bL:()=>Q,bm:()=>eo,hE:()=>en,hJ:()=>ee,l9:()=>Z});var r=n(2115),o=n(5185),i=n(6101),a=n(6081),l=n(1285),s=n(5845),u=n(9178),c=n(7900),d=n(4378),f=n(8905),p=n(3655),h=n(2293),m=n(3795),v=n(8168),g=n(9708),y=n(5155),b="Dialog",[w,x]=(0,a.A)(b),[k,S]=w(b),C=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:a,modal:u=!0}=e,c=r.useRef(null),d=r.useRef(null),[f=!1,p]=(0,s.i)({prop:o,defaultProp:i,onChange:a});return(0,y.jsx)(k,{scope:t,triggerRef:c,contentRef:d,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:u,children:n})};C.displayName=b;var E="DialogTrigger",M=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=S(E,n),l=(0,i.s)(t,a.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":V(a.open),...r,ref:l,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});M.displayName=E;var A="DialogPortal",[R,T]=w(A,{forceMount:void 0}),P=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,a=S(A,t);return(0,y.jsx)(R,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:n||a.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};P.displayName=A;var j="DialogOverlay",N=r.forwardRef((e,t)=>{let n=T(j,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=S(j,e.__scopeDialog);return i.modal?(0,y.jsx)(f.C,{present:r||i.open,children:(0,y.jsx)(O,{...o,ref:t})}):null});N.displayName=j;var O=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=S(j,n);return(0,y.jsx)(m.A,{as:g.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":V(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),D="DialogContent",L=r.forwardRef((e,t)=>{let n=T(D,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=S(D,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||i.open,children:i.modal?(0,y.jsx)(_,{...o,ref:t}):(0,y.jsx)(z,{...o,ref:t})})});L.displayName=D;var _=r.forwardRef((e,t)=>{let n=S(D,e.__scopeDialog),a=r.useRef(null),l=(0,i.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(F,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),z=r.forwardRef((e,t)=>{let n=S(D,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,y.jsx)(F,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(a=n.triggerRef.current)||void 0===a||a.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var r,a;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let l=t.target;(null===(a=n.triggerRef.current)||void 0===a?void 0:a.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),F=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:l,...s}=e,d=S(D,n),f=r.useRef(null),p=(0,i.s)(t,f);return(0,h.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,y.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":V(d.open),...s,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)($,{titleId:d.titleId}),(0,y.jsx)(K,{contentRef:f,descriptionId:d.descriptionId})]})]})}),I="DialogTitle",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=S(I,n);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});W.displayName=I;var H="DialogDescription",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=S(H,n);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});B.displayName=H;var q="DialogClose",G=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=S(q,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function V(e){return e?"open":"closed"}G.displayName=q;var Y="DialogTitleWarning",[U,X]=(0,a.q)(Y,{contentName:D,titleName:I,docsSlug:"dialog"}),$=e=>{let{titleId:t}=e,n=X(Y),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},K=e=>{let{contentRef:t,descriptionId:n}=e,o=X("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(i)},[i,t,n]),null},Q=C,Z=M,J=P,ee=N,et=L,en=W,er=B,eo=G},5525:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5657:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},5690:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},5845:(e,t,n)=>{"use strict";n.d(t,{i:()=>i});var r=n(2115),o=n(9033);function i({prop:e,defaultProp:t,onChange:n=()=>{}}){let[i,a]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[i]=n,a=r.useRef(i),l=(0,o.c)(t);return r.useEffect(()=>{a.current!==i&&(l(i),a.current=i)},[i,a,l]),n}({defaultProp:t,onChange:n}),l=void 0!==e,s=l?e:i,u=(0,o.c)(n);return[s,r.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&u(n)}else a(t)},[l,e,a,u])]}},5863:(e,t,n)=>{"use strict";n.d(t,{C1:()=>x,bL:()=>w});var r=n(2115),o=n(6081),i=n(3655),a=n(5155),l="Progress",[s,u]=(0,o.A)(l),[c,d]=s(l),f=r.forwardRef((e,t)=>{var n,r,o,l;let{__scopeProgress:s,value:u=null,max:d,getValueLabel:f=m,...p}=e;(d||0===d)&&!y(d)&&console.error((n="".concat(d),r="Progress","Invalid prop `max` of value `".concat(n,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let h=y(d)?d:100;null===u||b(u,h)||console.error((o="".concat(u),l="Progress","Invalid prop `value` of value `".concat(o,"` supplied to `").concat(l,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let w=b(u,h)?u:null,x=g(w)?f(w,h):void 0;return(0,a.jsx)(c,{scope:s,value:w,max:h,children:(0,a.jsx)(i.sG.div,{"aria-valuemax":h,"aria-valuemin":0,"aria-valuenow":g(w)?w:void 0,"aria-valuetext":x,role:"progressbar","data-state":v(w,h),"data-value":null!=w?w:void 0,"data-max":h,...p,ref:t})})});f.displayName=l;var p="ProgressIndicator",h=r.forwardRef((e,t)=>{var n;let{__scopeProgress:r,...o}=e,l=d(p,r);return(0,a.jsx)(i.sG.div,{"data-state":v(l.value,l.max),"data-value":null!==(n=l.value)&&void 0!==n?n:void 0,"data-max":l.max,...o,ref:t})});function m(e,t){return"".concat(Math.round(e/t*100),"%")}function v(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function y(e){return g(e)&&!isNaN(e)&&e>0}function b(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=p;var w=f,x=h},5968:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},5977:(e,t,n)=>{"use strict";n.d(t,{H4:()=>x,_V:()=>w,bL:()=>b});var r=n(2115),o=n(6081),i=n(9033),a=n(2712),l=n(3655),s=n(5155),u="Avatar",[c,d]=(0,o.A)(u),[f,p]=c(u),h=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,a]=r.useState("idle");return(0,s.jsx)(f,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,s.jsx)(l.sG.span,{...o,ref:t})})});h.displayName=u;var m="AvatarImage",v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:u=()=>{},...c}=e,d=p(m,n),f=function(e,t){let[n,o]=r.useState("idle");return(0,a.N)(()=>{if(!e){o("error");return}let n=!0,r=new window.Image,i=e=>()=>{n&&o(e)};return o("loading"),r.onload=i("loaded"),r.onerror=i("error"),r.src=e,t&&(r.referrerPolicy=t),()=>{n=!1}},[e,t]),n}(o,c.referrerPolicy),h=(0,i.c)(e=>{u(e),d.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==f&&h(f)},[f,h]),"loaded"===f?(0,s.jsx)(l.sG.img,{...c,ref:t,src:o}):null});v.displayName=m;var g="AvatarFallback",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,a=p(g,n),[u,c]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==a.imageLoadingStatus?(0,s.jsx)(l.sG.span,{...i,ref:t}):null});y.displayName=g;var b=h,w=v,x=y},6081:(e,t,n)=>{"use strict";n.d(t,{A:()=>a,q:()=>i});var r=n(2115),o=n(5155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),l=n.length;n=[...n,i];let s=t=>{let{scope:n,children:i,...s}=t,u=n?.[e]?.[l]||a,c=r.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:c,children:i})};return s.displayName=t+"Provider",[s,function(n,o){let s=o?.[e]?.[l]||a,u=r.useContext(s);if(u)return u;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},6101:(e,t,n)=>{"use strict";n.d(t,{s:()=>a,t:()=>i});var r=n(2115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function a(...e){return r.useCallback(i(...e),e)}},6140:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Ruler",[["path",{d:"M21.3 15.3a2.4 2.4 0 0 1 0 3.4l-2.6 2.6a2.4 2.4 0 0 1-3.4 0L2.7 8.7a2.41 2.41 0 0 1 0-3.4l2.6-2.6a2.41 2.41 0 0 1 3.4 0Z",key:"icamh8"}],["path",{d:"m14.5 12.5 2-2",key:"inckbg"}],["path",{d:"m11.5 9.5 2-2",key:"fmmyf7"}],["path",{d:"m8.5 6.5 2-2",key:"vc6u1g"}],["path",{d:"m17.5 15.5 2-2",key:"wo5hmg"}]])},6474:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6517:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},6767:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},6785:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},7340:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},7434:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7489:(e,t,n)=>{"use strict";n.d(t,{b:()=>u});var r=n(2115),o=n(3655),i=n(5155),a="horizontal",l=["horizontal","vertical"],s=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:s=a,...u}=e,c=(n=s,l.includes(n))?s:a;return(0,i.jsx)(o.sG.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...u,ref:t})});s.displayName="Separator";var u=s},7580:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7863:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},7900:(e,t,n)=>{"use strict";n.d(t,{n:()=>d});var r=n(2115),o=n(6101),i=n(3655),a=n(9033),l=n(5155),s="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[b,w]=r.useState(null),x=(0,a.c)(v),k=(0,a.c)(g),S=r.useRef(null),C=(0,o.s)(t,e=>w(e)),E=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(E.paused||!b)return;let t=e.target;b.contains(t)?S.current=t:h(S.current,{select:!0})},t=function(e){if(E.paused||!b)return;let t=e.relatedTarget;null===t||b.contains(t)||h(S.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,b,E.paused]),r.useEffect(()=>{if(b){m.add(E);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(s,c);b.addEventListener(s,x),b.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(b))}return()=>{b.removeEventListener(s,x),setTimeout(()=>{let t=new CustomEvent(u,c);b.addEventListener(u,k),b.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),b.removeEventListener(u,k),m.remove(E)},0)}}},[b,x,k,E]);let M=r.useCallback(e=>{if(!n&&!d||E.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,E.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:M})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=v(e,t)).unshift(t)},remove(t){var n;null===(n=(e=v(e,t))[0])||void 0===n||n.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},7901:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Scan",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}]])},7924:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},8168:(e,t,n)=>{"use strict";n.d(t,{Eq:()=>c});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,s=function(e){return e&&(e.host||s(e.parentNode))},u=function(e,t,n,r){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=s(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var c=a[n],d=[],f=new Set,p=new Set(u),h=function(e){!(!e||f.has(e))&&(f.add(e),h(e.parentNode))};u.forEach(h);var m=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,s=(c.get(e)||0)+1;o.set(e,l),c.set(e,s),d.push(e),1===l&&a&&i.set(e,!0),1===s&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,a=c.get(e)-1;o.set(e,t),c.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),u(o,i,n,"aria-hidden")):function(){return null}}},8375:()=>{},8611:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Gauge",[["path",{d:"m12 14 4-4",key:"9kzdfg"}],["path",{d:"M3.34 19a10 10 0 1 1 17.32 0",key:"19p75a"}]])},8749:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8795:(e,t,n)=>{"use strict";n.d(t,{Mz:()=>e8,i3:()=>tt,UC:()=>te,bL:()=>e7,Bk:()=>eY});var r=n(2115);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,s=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function b(e){return e.replace(/start|end/g,e=>d[e])}let w=["left","right"],x=["right","left"],k=["top","bottom"],S=["bottom","top"];function C(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function E(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function M(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function A(e,t,n){let r,{reference:o,floating:i}=e,a=y(t),l=m(y(t)),s=v(l),u=p(t),c="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,g=o[s]/2-i[s]/2;switch(u){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[l]-=g*(n&&c?-1:1);break;case"end":r[l]+=g*(n&&c?-1:1)}return r}let R=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),s=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=A(u,r,s),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:b}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(u=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:c,y:d}=A(u,f,s)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function T(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=E(h),v=l[p?"floating"===d?"reference":"floating":d],g=M(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:c,strategy:s})),y="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),w=await (null==i.isElement?void 0:i.isElement(b))&&await (null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},x=M(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:b,strategy:s}):y);return{top:(g.top-x.top+m.top)/w.y,bottom:(x.bottom-g.bottom+m.bottom)/w.y,left:(g.left-x.left+m.left)/w.x,right:(x.right-g.right+m.right)/w.x}}function P(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function j(e){return o.some(t=>e[t]>=0)}let N=new Set(["left","top"]);async function O(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=h(n),s="y"===y(n),u=N.has(a)?-1:1,c=i&&s?-1:1,d=f(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof g&&(v="end"===l?-1*g:g),s?{x:v*c,y:m*u}:{x:m*u,y:v*c}}function D(){return"undefined"!=typeof window}function L(e){return F(e)?(e.nodeName||"").toLowerCase():"#document"}function _(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function z(e){var t;return null==(t=(F(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function F(e){return!!D()&&(e instanceof Node||e instanceof _(e).Node)}function I(e){return!!D()&&(e instanceof Element||e instanceof _(e).Element)}function W(e){return!!D()&&(e instanceof HTMLElement||e instanceof _(e).HTMLElement)}function H(e){return!!D()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof _(e).ShadowRoot)}let B=new Set(["inline","contents"]);function q(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!B.has(o)}let G=new Set(["table","td","th"]),V=[":popover-open",":modal"];function Y(e){return V.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let U=["transform","translate","scale","rotate","perspective"],X=["transform","translate","scale","rotate","perspective","filter"],$=["paint","layout","strict","content"];function K(e){let t=Q(),n=I(e)?ee(e):e;return U.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||X.some(e=>(n.willChange||"").includes(e))||$.some(e=>(n.contain||"").includes(e))}function Q(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Z=new Set(["html","body","#document"]);function J(e){return Z.has(L(e))}function ee(e){return _(e).getComputedStyle(e)}function et(e){return I(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===L(e))return e;let t=e.assignedSlot||e.parentNode||H(e)&&e.host||z(e);return H(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=en(t);return J(n)?t.ownerDocument?t.ownerDocument.body:t.body:W(n)&&q(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=_(o);if(i){let e=eo(a);return t.concat(a,a.visualViewport||[],q(o)?o:[],e&&n?er(e):[])}return t.concat(o,er(o,[],n))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ei(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=W(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,s=l(n)!==i||l(r)!==a;return s&&(n=i,r=a),{width:n,height:r,$:s}}function ea(e){return I(e)?e:e.contextElement}function el(e){let t=ea(e);if(!W(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ei(t),a=(i?l(n.width):n.width)/r,s=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}let es=u(0);function eu(e){let t=_(e);return Q()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:es}function ec(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=ea(e),l=u(1);t&&(r?I(r)&&(l=el(r)):l=el(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===_(a))&&o)?eu(a):u(0),c=(i.left+s.x)/l.x,d=(i.top+s.y)/l.y,f=i.width/l.x,p=i.height/l.y;if(a){let e=_(a),t=r&&I(r)?_(r):r,n=e,o=eo(n);for(;o&&r&&t!==n;){let e=el(o),t=o.getBoundingClientRect(),r=ee(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=i,d+=a,o=eo(n=_(o))}}return M({width:f,height:p,x:c,y:d})}function ed(e,t){let n=et(e).scrollLeft;return t?t.left+n:ec(z(e)).left+n}function ef(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ed(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function eh(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=_(e),r=z(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,s=0;if(o){i=o.width,a=o.height;let e=Q();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,s=o.offsetTop)}return{width:i,height:a,x:l,y:s}}(e,n);else if("document"===t)r=function(e){let t=z(e),n=et(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+ed(e),s=-n.scrollTop;return"rtl"===ee(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:s}}(z(e));else if(I(t))r=function(e,t){let n=ec(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=W(e)?el(e):u(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=eu(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return M(r)}function em(e){return"static"===ee(e).position}function ev(e,t){if(!W(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return z(e)===n&&(n=n.ownerDocument.body),n}function eg(e,t){var n;let r=_(e);if(Y(e))return r;if(!W(e)){let t=en(e);for(;t&&!J(t);){if(I(t)&&!em(t))return t;t=en(t)}return r}let o=ev(e,t);for(;o&&(n=o,G.has(L(n)))&&em(o);)o=ev(o,t);return o&&J(o)&&em(o)&&!K(o)?r:o||function(e){let t=en(e);for(;W(t)&&!J(t);){if(K(t))return t;if(Y(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||eg,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=W(t),o=z(t),i="fixed"===n,a=ec(e,!0,i,t),l={scrollLeft:0,scrollTop:0},s=u(0);if(r||!r&&!i){if(("body"!==L(t)||q(o))&&(l=et(t)),r){let e=ec(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=ed(o))}i&&!r&&o&&(s.x=ed(o));let c=!o||r||i?u(0):ef(o,l);return{x:a.left+l.scrollLeft-s.x-c.x,y:a.top+l.scrollTop-s.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eb={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=z(r),l=!!t&&Y(t.floating);if(r===a||l&&i)return n;let s={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),f=W(r);if((f||!f&&!i)&&(("body"!==L(r)||q(a))&&(s=et(r)),W(r))){let e=ec(r);c=el(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!a||f||i?u(0):ef(a,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-s.scrollTop*c.y+d.y+p.y}},getDocumentElement:z,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?Y(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>I(e)&&"body"!==L(e)),o=null,i="fixed"===ee(e).position,a=i?en(e):e;for(;I(a)&&!J(a);){let t=ee(a),n=K(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ep.has(o.position)||q(a)&&!n&&function e(t,n){let r=en(t);return!(r===n||!I(r)||J(r))&&("fixed"===ee(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=en(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=l[0],u=l.reduce((e,n)=>{let r=eh(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},eh(t,s,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ei(e);return{width:t,height:n}},getScale:el,isElement:I,isRTL:function(e){return"rtl"===ee(e).direction}};function ew(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ex=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:s,elements:u,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let g=E(p),b={x:n,y:r},w=m(y(o)),x=v(w),k=await s.getDimensions(d),S="y"===w,C=S?"clientHeight":"clientWidth",M=l.reference[x]+l.reference[w]-b[w]-l.floating[x],A=b[w]-l.reference[w],R=await (null==s.getOffsetParent?void 0:s.getOffsetParent(d)),T=R?R[C]:0;T&&await (null==s.isElement?void 0:s.isElement(R))||(T=u.floating[C]||l.floating[x]);let P=T/2-k[x]/2-1,j=i(g[S?"top":"left"],P),N=i(g[S?"bottom":"right"],P),O=T-k[x]-N,D=T/2-k[x]/2+(M/2-A/2),L=a(j,i(D,O)),_=!c.arrow&&null!=h(o)&&D!==L&&l.reference[x]/2-(D<j?j:N)-k[x]/2<0,z=_?D<j?D-j:D-O:0;return{[w]:b[w]+z,data:{[w]:L,centerOffset:D-L-z,..._&&{alignmentOffset:z}},reset:_}}}),ek=(e,t,n)=>{let r=new Map,o={platform:eb,...n},i={...o.platform,_c:r};return R(e,t,{...o,platform:i})};var eS=n(7650),eC="undefined"!=typeof document?r.useLayoutEffect:function(){};function eE(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eE(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eE(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eM(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eA(e,t){let n=eM(e);return Math.round(t*n)/n}function eR(e){let t=r.useRef(e);return eC(()=>{t.current=e}),t}let eT=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ex({element:n.current,padding:r}).fn(t):{}:n?ex({element:n,padding:r}).fn(t):{}}}),eP=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,s=await O(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:a}}}}}(e),options:[e,t]}),ej=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=f(e,t),d={x:n,y:r},h=await T(t,c),v=y(p(o)),g=m(v),b=d[g],w=d[v];if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=b+h[e],r=b-h[t];b=a(n,i(b,r))}if(s){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,i(w,r))}let x=u.fn({...t,[g]:b,[v]:w});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[g]:l,[v]:s}}}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:s=!0,crossAxis:u=!0}=f(e,t),c={x:n,y:r},d=y(o),h=m(d),v=c[h],g=c[d],b=f(l,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(s){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+w.mainAxis,n=i.reference[h]+i.reference[e]-w.mainAxis;v<t?v=t:v>n&&(v=n)}if(u){var x,k;let e="y"===h?"width":"height",t=N.has(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[d])||0)+(t?0:w.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(k=a.offset)?void 0:k[d])||0)-(t?w.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[h]:v,[d]:g}}}}(e),options:[e,t]}),eO=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:g}=t,{mainAxis:E=!0,crossAxis:M=!0,fallbackPlacements:A,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:j=!0,...N}=f(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let O=p(l),D=y(c),L=p(c)===c,_=await (null==d.isRTL?void 0:d.isRTL(g.floating)),z=A||(L||!j?[C(c)]:function(e){let t=C(e);return[b(e),t,b(t)]}(c)),F="none"!==P;!A&&F&&z.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?x:w;return t?w:x;case"left":case"right":return t?k:S;default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(b)))),i}(c,j,P,_));let I=[c,...z],W=await T(t,N),H=[],B=(null==(r=s.flip)?void 0:r.overflows)||[];if(E&&H.push(W[O]),M){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(y(e)),i=v(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=C(a)),[a,C(a)]}(l,u,_);H.push(W[e[0]],W[e[1]])}if(B=[...B,{placement:l,overflows:H}],!H.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=I[e];if(t&&("alignment"!==M||D===y(t)||B.every(e=>e.overflows[0]>0&&y(e.placement)===D)))return{data:{index:e,overflows:B},reset:{placement:t}};let n=null==(i=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(R){case"bestFit":{let e=null==(a=B.filter(e=>{if(F){let t=y(e.placement);return t===D||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=c}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l;let{placement:s,rects:u,platform:c,elements:d}=t,{apply:m=()=>{},...v}=f(e,t),g=await T(t,v),b=p(s),w=h(s),x="y"===y(s),{width:k,height:S}=u.floating;"top"===b||"bottom"===b?(o=b,l=w===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(l=b,o="end"===w?"top":"bottom");let C=S-g.top-g.bottom,E=k-g.left-g.right,M=i(S-g[o],C),A=i(k-g[l],E),R=!t.middlewareData.shift,P=M,j=A;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(j=E),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=C),R&&!w){let e=a(g.left,0),t=a(g.right,0),n=a(g.top,0),r=a(g.bottom,0);x?j=k-2*(0!==e||0!==t?e+t:a(g.left,g.right)):P=S-2*(0!==n||0!==r?n+r:a(g.top,g.bottom))}await m({...t,availableWidth:j,availableHeight:P});let N=await c.getDimensions(d.floating);return k!==N.width||S!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eL=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=P(await T(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:j(e)}}}case"escaped":{let e=P(await T(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:j(e)}}}default:return{}}}}}(e),options:[e,t]}),e_=(e,t)=>({...eT(e),options:[e,t]});var ez=n(3655),eF=n(5155),eI=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eF.jsx)(ez.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eF.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eI.displayName="Arrow";var eW=n(6101),eH=n(6081),eB=n(9033),eq=n(2712),eG="Popper",[eV,eY]=(0,eH.A)(eG),[eU,eX]=eV(eG),e$=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eF.jsx)(eU,{scope:t,anchor:o,onAnchorChange:i,children:n})};e$.displayName=eG;var eK="PopperAnchor",eQ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eX(eK,n),l=r.useRef(null),s=(0,eW.s)(t,l);return r.useEffect(()=>{a.onAnchorChange((null==o?void 0:o.current)||l.current)}),o?null:(0,eF.jsx)(ez.sG.div,{...i,ref:s})});eQ.displayName=eK;var eZ="PopperContent",[eJ,e0]=eV(eZ),e1=r.forwardRef((e,t)=>{var n,o,l,u,c,d,f,p;let{__scopePopper:h,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:b=0,avoidCollisions:w=!0,collisionBoundary:x=[],collisionPadding:k=0,sticky:S="partial",hideWhenDetached:C=!1,updatePositionStrategy:E="optimized",onPlaced:M,...A}=e,R=eX(eZ,h),[T,P]=r.useState(null),j=(0,eW.s)(t,e=>P(e)),[N,O]=r.useState(null),D=function(e){let[t,n]=r.useState(void 0);return(0,eq.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(N),L=null!==(f=null==D?void 0:D.width)&&void 0!==f?f:0,_=null!==(p=null==D?void 0:D.height)&&void 0!==p?p:0,F="number"==typeof k?k:{top:0,right:0,bottom:0,left:0,...k},I=Array.isArray(x)?x:[x],W=I.length>0,H={padding:F,boundary:I.filter(e9),altBoundary:W},{refs:B,floatingStyles:q,placement:G,isPositioned:V,middlewareData:Y}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);eE(p,o)||h(o);let[m,v]=r.useState(null),[g,y]=r.useState(null),b=r.useCallback(e=>{e!==S.current&&(S.current=e,v(e))},[]),w=r.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),x=a||m,k=l||g,S=r.useRef(null),C=r.useRef(null),E=r.useRef(d),M=null!=u,A=eR(u),R=eR(i),T=eR(c),P=r.useCallback(()=>{if(!S.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};R.current&&(e.platform=R.current),ek(S.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};j.current&&!eE(E.current,t)&&(E.current=t,eS.flushSync(()=>{f(t)}))})},[p,t,n,R,T]);eC(()=>{!1===c&&E.current.isPositioned&&(E.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let j=r.useRef(!1);eC(()=>(j.current=!0,()=>{j.current=!1}),[]),eC(()=>{if(x&&(S.current=x),k&&(C.current=k),x&&k){if(A.current)return A.current(x,k,P);P()}},[x,k,P,A,M]);let N=r.useMemo(()=>({reference:S,floating:C,setReference:b,setFloating:w}),[b,w]),O=r.useMemo(()=>({reference:x,floating:k}),[x,k]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=eA(O.floating,d.x),r=eA(O.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...eM(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,O.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:P,refs:N,elements:O,floatingStyles:D}),[d,P,N,O,D])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=ea(e),h=l||u?[...p?er(p):[],...er(t)]:[];h.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&d?function(e,t){let n,r=null,o=z(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=f;if(c||t(),!m||!v)return;let g=s(h),y=s(o.clientWidth-(p+m)),b={rootMargin:-g+"px "+-y+"px "+-s(o.clientHeight-(h+v))+"px "+-s(p)+"px",threshold:a(0,i(1,d))||1},w=!0;function x(t){let r=t[0].intersectionRatio;if(r!==d){if(!w)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ew(f,e.getBoundingClientRect())||u(),w=!1}try{r=new IntersectionObserver(x,{...b,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,b)}r.observe(e)}(!0),l}(p,n):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?ec(e):null;return f&&function t(){let r=ec(e);y&&!ew(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{l&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===E})},elements:{reference:R.anchor},middleware:[eP({mainAxis:v+_,alignmentAxis:y}),w&&ej({mainAxis:!0,crossAxis:!1,limiter:"partial"===S?eN():void 0,...H}),w&&eO({...H}),eD({...H,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:a}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(r,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),N&&e_({element:N,padding:b}),e3({arrowWidth:L,arrowHeight:_}),C&&eL({strategy:"referenceHidden",...H})]}),[U,X]=e6(G),$=(0,eB.c)(M);(0,eq.N)(()=>{V&&(null==$||$())},[V,$]);let K=null===(n=Y.arrow)||void 0===n?void 0:n.x,Q=null===(o=Y.arrow)||void 0===o?void 0:o.y,Z=(null===(l=Y.arrow)||void 0===l?void 0:l.centerOffset)!==0,[J,ee]=r.useState();return(0,eq.N)(()=>{T&&ee(window.getComputedStyle(T).zIndex)},[T]),(0,eF.jsx)("div",{ref:B.setFloating,"data-radix-popper-content-wrapper":"",style:{...q,transform:V?q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:J,"--radix-popper-transform-origin":[null===(u=Y.transformOrigin)||void 0===u?void 0:u.x,null===(c=Y.transformOrigin)||void 0===c?void 0:c.y].join(" "),...(null===(d=Y.hide)||void 0===d?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eF.jsx)(eJ,{scope:h,placedSide:U,onArrowChange:O,arrowX:K,arrowY:Q,shouldHideArrow:Z,children:(0,eF.jsx)(ez.sG.div,{"data-side":U,"data-align":X,...A,ref:j,style:{...A.style,animation:V?void 0:"none"}})})})});e1.displayName=eZ;var e2="PopperArrow",e5={top:"bottom",right:"left",bottom:"top",left:"right"},e4=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e0(e2,n),i=e5[o.placedSide];return(0,eF.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eF.jsx)(eI,{...r,ref:t,style:{...r.style,display:"block"}})})});function e9(e){return null!==e}e4.displayName=e2;var e3=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,a;let{placement:l,rects:s,middlewareData:u}=t,c=(null===(n=u.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,h]=e6(l),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!==(i=null===(r=u.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+d/2,g=(null!==(a=null===(o=u.arrow)||void 0===o?void 0:o.y)&&void 0!==a?a:0)+f/2,y="",b="";return"bottom"===p?(y=c?m:"".concat(v,"px"),b="".concat(-f,"px")):"top"===p?(y=c?m:"".concat(v,"px"),b="".concat(s.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),b=c?m:"".concat(g,"px")):"left"===p&&(y="".concat(s.floating.width+f,"px"),b=c?m:"".concat(g,"px")),{data:{x:y,y:b}}}});function e6(e){let[t,n="center"]=e.split("-");return[t,n]}var e7=e$,e8=eQ,te=e1,tt=e4},8905:(e,t,n)=>{"use strict";n.d(t,{C:()=>a});var r=n(2115),o=n(6101),i=n(2712),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),s=r.useRef({}),u=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(s.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=s.current,n=u.current;if(n!==e){let r=c.current,o=l(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,i.N)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=l(s.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=l(s.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(s.current=getComputedStyle(e)),a(e)},[])}}(t),s="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),u=(0,o.s)(a.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof n||a.isPresent?r.cloneElement(s,{ref:u}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},9033:(e,t,n)=>{"use strict";n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9074:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9137:(e,t,n)=>{"use strict";e.exports=n(2269).style},9178:(e,t,n)=>{"use strict";n.d(t,{qW:()=>f});var r,o=n(2115),i=n(5185),a=n(3655),l=n(6101),s=n(9033),u=n(5155),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:w,...x}=e,k=o.useContext(d),[S,C]=o.useState(null),E=null!==(f=null==S?void 0:S.ownerDocument)&&void 0!==f?f:null===(n=globalThis)||void 0===n?void 0:n.document,[,M]=o.useState({}),A=(0,l.s)(t,e=>C(e)),R=Array.from(k.layers),[T]=[...k.layersWithOutsidePointerEventsDisabled].slice(-1),P=R.indexOf(T),j=S?R.indexOf(S):-1,N=k.layersWithOutsidePointerEventsDisabled.size>0,O=j>=P,D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,s.c)(e),i=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...k.branches].some(e=>e.contains(t));!O||n||(null==g||g(e),null==b||b(e),e.defaultPrevented||null==w||w())},E),L=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,s.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...k.branches].some(e=>e.contains(t))||(null==y||y(e),null==b||b(e),e.defaultPrevented||null==w||w())},E);return!function(e,t=globalThis?.document){let n=(0,s.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{j===k.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},E),o.useEffect(()=>{if(S)return m&&(0===k.layersWithOutsidePointerEventsDisabled.size&&(r=E.body.style.pointerEvents,E.body.style.pointerEvents="none"),k.layersWithOutsidePointerEventsDisabled.add(S)),k.layers.add(S),p(),()=>{m&&1===k.layersWithOutsidePointerEventsDisabled.size&&(E.body.style.pointerEvents=r)}},[S,E,m,k]),o.useEffect(()=>()=>{S&&(k.layers.delete(S),k.layersWithOutsidePointerEventsDisabled.delete(S),p())},[S,k]),o.useEffect(()=>{let e=()=>M({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(a.sG.div,{...x,ref:A,style:{pointerEvents:N?O?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,D.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,a.hO)(i,l):i.dispatchEvent(l)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},9397:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},9420:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},9613:(e,t,n)=>{"use strict";n.d(t,{Kq:()=>I,UC:()=>B,bL:()=>W,l9:()=>H});var r=n(2115),o=n(5185),i=n(6101),a=n(6081),l=n(9178),s=n(1285),u=n(8795),c=(n(4378),n(8905)),d=n(3655),f=n(9708),p=n(5845),h=n(2564),m=n(5155),[v,g]=(0,a.A)("Tooltip",[u.Bk]),y=(0,u.Bk)(),b="TooltipProvider",w="tooltip.open",[x,k]=v(b),S=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:a}=e,[l,s]=r.useState(!0),u=r.useRef(!1),c=r.useRef(0);return r.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,m.jsx)(x,{scope:t,isOpenDelayed:l,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(c.current),s(!1)},[]),onClose:r.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>s(!0),o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:r.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:a})};S.displayName=b;var C="Tooltip",[E,M]=v(C),A=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:i=!1,onOpenChange:a,disableHoverableContent:l,delayDuration:c}=e,d=k(C,e.__scopeTooltip),f=y(t),[h,v]=r.useState(null),g=(0,s.B)(),b=r.useRef(0),x=null!=l?l:d.disableHoverableContent,S=null!=c?c:d.delayDuration,M=r.useRef(!1),[A=!1,R]=(0,p.i)({prop:o,defaultProp:i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(w))):d.onClose(),null==a||a(e)}}),T=r.useMemo(()=>A?M.current?"delayed-open":"instant-open":"closed",[A]),P=r.useCallback(()=>{window.clearTimeout(b.current),b.current=0,M.current=!1,R(!0)},[R]),j=r.useCallback(()=>{window.clearTimeout(b.current),b.current=0,R(!1)},[R]),N=r.useCallback(()=>{window.clearTimeout(b.current),b.current=window.setTimeout(()=>{M.current=!0,R(!0),b.current=0},S)},[S,R]);return r.useEffect(()=>()=>{b.current&&(window.clearTimeout(b.current),b.current=0)},[]),(0,m.jsx)(u.bL,{...f,children:(0,m.jsx)(E,{scope:t,contentId:g,open:A,stateAttribute:T,trigger:h,onTriggerChange:v,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayed?N():P()},[d.isOpenDelayed,N,P]),onTriggerLeave:r.useCallback(()=>{x?j():(window.clearTimeout(b.current),b.current=0)},[j,x]),onOpen:P,onClose:j,disableHoverableContent:x,children:n})})};A.displayName=C;var R="TooltipTrigger",T=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...a}=e,l=M(R,n),s=k(R,n),c=y(n),f=r.useRef(null),p=(0,i.s)(t,f,l.onTriggerChange),h=r.useRef(!1),v=r.useRef(!1),g=r.useCallback(()=>h.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,m.jsx)(u.Mz,{asChild:!0,...c,children:(0,m.jsx)(d.sG.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...a,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"===e.pointerType||v.current||s.isPointerInTransitRef.current||(l.onTriggerEnter(),v.current=!0)}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{l.onTriggerLeave(),v.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{h.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||l.onOpen()}),onBlur:(0,o.m)(e.onBlur,l.onClose),onClick:(0,o.m)(e.onClick,l.onClose)})})});T.displayName=R;var[P,j]=v("TooltipPortal",{forceMount:void 0}),N="TooltipContent",O=r.forwardRef((e,t)=>{let n=j(N,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,a=M(N,e.__scopeTooltip);return(0,m.jsx)(c.C,{present:r||a.open,children:a.disableHoverableContent?(0,m.jsx)(z,{side:o,...i,ref:t}):(0,m.jsx)(D,{side:o,...i,ref:t})})}),D=r.forwardRef((e,t)=>{let n=M(N,e.__scopeTooltip),o=k(N,e.__scopeTooltip),a=r.useRef(null),l=(0,i.s)(t,a),[s,u]=r.useState(null),{trigger:c,onClose:d}=n,f=a.current,{onPointerInTransitChange:p}=o,h=r.useCallback(()=>{u(null),p(!1)},[p]),v=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:+!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),p(!0)},[p]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(c&&f){let e=e=>v(e,f),t=e=>v(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,v,h]),r.useEffect(()=>{if(s){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e].x,l=t[e].y,s=t[i].x,u=t[i].y;l>r!=u>r&&n<(s-a)*(r-l)/(u-l)+a&&(o=!o)}return o}(n,s);r?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,s,d,h]),(0,m.jsx)(z,{...e,ref:l})}),[L,_]=v(C,{isInside:!1}),z=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:a,onPointerDownOutside:s,...c}=e,d=M(N,n),p=y(n),{onClose:v}=d;return r.useEffect(()=>(document.addEventListener(w,v),()=>document.removeEventListener(w,v)),[v]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&v()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,v]),(0,m.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:v,children:(0,m.jsxs)(u.UC,{"data-state":d.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,m.jsx)(f.xV,{children:o}),(0,m.jsx)(L,{scope:n,isInside:!0,children:(0,m.jsx)(h.b,{id:d.contentId,role:"tooltip",children:i||o})})]})})});O.displayName=N;var F="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=y(n);return _(F,n).isInside?null:(0,m.jsx)(u.i3,{...o,...r,ref:t})}).displayName=F;var I=S,W=A,H=T,B=O},9688:(e,t,n)=>{"use strict";n.d(t,{QP:()=>$});let r=e=>{let t=l(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),o(n,t)||a(e)},getConflictingClassGroupIds:(e,t)=>{let o=n[e]||[];return t&&r[e]?[...o,...r[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let n=e[0],r=t.nextPart.get(n),i=r?o(e.slice(1),r):void 0;if(i)return i;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},i=/^\[(.+)\]$/,a=e=>{if(i.test(e)){let t=i.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},l=e=>{let{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return d(Object.entries(e.classGroups),n).forEach(([e,n])=>{s(n,r,e,t)}),r},s=(e,t,n,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=n;return}if("function"==typeof e){if(c(e)){s(e(r),t,n,r);return}t.validators.push({validator:e,classGroupId:n});return}Object.entries(e).forEach(([e,o])=>{s(o,u(t,e),n,r)})})},u=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},c=e=>e.isThemeGetter,d=(e,t)=>t?e.map(([e,n])=>[e,n.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,n])=>[t+e,n])):e)]):e,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,r=new Map,o=(o,i)=>{n.set(o,i),++t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(o(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):o(e,t)}}},p=e=>{let{separator:t,experimentalParseClassName:n}=e,r=1===t.length,o=t[0],i=t.length,a=e=>{let n;let a=[],l=0,s=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===l){if(c===o&&(r||e.slice(u,u+i)===t)){a.push(e.slice(s,u)),s=u+i;continue}if("/"===c){n=u;continue}}"["===c?l++:"]"===c&&l--}let u=0===a.length?e:e.substring(s),c=u.startsWith("!"),d=c?u.substring(1):u;return{modifiers:a,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:n&&n>s?n-s:void 0}};return n?e=>n({className:e,parseClassName:a}):a},h=e=>{if(e.length<=1)return e;let t=[],n=[];return e.forEach(e=>{"["===e[0]?(t.push(...n.sort(),e),n=[]):n.push(e)}),t.push(...n.sort()),t},m=e=>({cache:f(e.cacheSize),parseClassName:p(e),...r(e)}),v=/\s+/,g=(e,t)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],a=e.trim().split(v),l="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{modifiers:s,hasImportantModifier:u,baseClassName:c,maybePostfixModifierPosition:d}=n(t),f=!!d,p=r(f?c.substring(0,d):c);if(!p){if(!f||!(p=r(c))){l=t+(l.length>0?" "+l:l);continue}f=!1}let m=h(s).join(":"),v=u?m+"!":m,g=v+p;if(i.includes(g))continue;i.push(g);let y=o(p,f);for(let e=0;e<y.length;++e){let t=y[e];i.push(v+t)}l=t+(l.length>0?" "+l:l)}return l};function y(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=b(e))&&(r&&(r+=" "),r+=t);return r}let b=e=>{let t;if("string"==typeof e)return e;let n="";for(let r=0;r<e.length;r++)e[r]&&(t=b(e[r]))&&(n&&(n+=" "),n+=t);return n},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:([a-z-]+):)?(.+)\]$/i,k=/^\d+\/\d+$/,S=new Set(["px","full","screen"]),C=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,E=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,M=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,A=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,R=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,T=e=>j(e)||S.has(e)||k.test(e),P=e=>G(e,"length",V),j=e=>!!e&&!Number.isNaN(Number(e)),N=e=>G(e,"number",j),O=e=>!!e&&Number.isInteger(Number(e)),D=e=>e.endsWith("%")&&j(e.slice(0,-1)),L=e=>x.test(e),_=e=>C.test(e),z=new Set(["length","size","percentage"]),F=e=>G(e,z,Y),I=e=>G(e,"position",Y),W=new Set(["image","url"]),H=e=>G(e,W,X),B=e=>G(e,"",U),q=()=>!0,G=(e,t,n)=>{let r=x.exec(e);return!!r&&(r[1]?"string"==typeof t?r[1]===t:t.has(r[1]):n(r[2]))},V=e=>E.test(e)&&!M.test(e),Y=()=>!1,U=e=>A.test(e),X=e=>R.test(e);Symbol.toStringTag;let $=function(e,...t){let n,r,o;let i=function(l){return r=(n=m(t.reduce((e,t)=>t(e),e()))).cache.get,o=n.cache.set,i=a,a(l)};function a(e){let t=r(e);if(t)return t;let i=g(e,n);return o(e,i),i}return function(){return i(y.apply(null,arguments))}}(()=>{let e=w("colors"),t=w("spacing"),n=w("blur"),r=w("brightness"),o=w("borderColor"),i=w("borderRadius"),a=w("borderSpacing"),l=w("borderWidth"),s=w("contrast"),u=w("grayscale"),c=w("hueRotate"),d=w("invert"),f=w("gap"),p=w("gradientColorStops"),h=w("gradientColorStopPositions"),m=w("inset"),v=w("margin"),g=w("opacity"),y=w("padding"),b=w("saturate"),x=w("scale"),k=w("sepia"),S=w("skew"),C=w("space"),E=w("translate"),M=()=>["auto","contain","none"],A=()=>["auto","hidden","clip","visible","scroll"],R=()=>["auto",L,t],z=()=>[L,t],W=()=>["",T,P],G=()=>["auto",j,L],V=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Y=()=>["solid","dashed","dotted","double","none"],U=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],X=()=>["start","end","center","between","around","evenly","stretch"],$=()=>["","0",L],K=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Q=()=>[j,L];return{cacheSize:500,separator:":",theme:{colors:[q],spacing:[T,P],blur:["none","",_,L],brightness:Q(),borderColor:[e],borderRadius:["none","","full",_,L],borderSpacing:z(),borderWidth:W(),contrast:Q(),grayscale:$(),hueRotate:Q(),invert:$(),gap:z(),gradientColorStops:[e],gradientColorStopPositions:[D,P],inset:R(),margin:R(),opacity:Q(),padding:z(),saturate:Q(),scale:Q(),sepia:$(),skew:Q(),space:z(),translate:z()},classGroups:{aspect:[{aspect:["auto","square","video",L]}],container:["container"],columns:[{columns:[_]}],"break-after":[{"break-after":K()}],"break-before":[{"break-before":K()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...V(),L]}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:M()}],"overscroll-x":[{"overscroll-x":M()}],"overscroll-y":[{"overscroll-y":M()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",O,L]}],basis:[{basis:R()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",L]}],grow:[{grow:$()}],shrink:[{shrink:$()}],order:[{order:["first","last","none",O,L]}],"grid-cols":[{"grid-cols":[q]}],"col-start-end":[{col:["auto",{span:["full",O,L]},L]}],"col-start":[{"col-start":G()}],"col-end":[{"col-end":G()}],"grid-rows":[{"grid-rows":[q]}],"row-start-end":[{row:["auto",{span:[O,L]},L]}],"row-start":[{"row-start":G()}],"row-end":[{"row-end":G()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",L]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",L]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...X()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...X(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...X(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[v]}],mx:[{mx:[v]}],my:[{my:[v]}],ms:[{ms:[v]}],me:[{me:[v]}],mt:[{mt:[v]}],mr:[{mr:[v]}],mb:[{mb:[v]}],ml:[{ml:[v]}],"space-x":[{"space-x":[C]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[C]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",L,t]}],"min-w":[{"min-w":[L,t,"min","max","fit"]}],"max-w":[{"max-w":[L,t,"none","full","min","max","fit","prose",{screen:[_]},_]}],h:[{h:[L,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[L,t,"auto","min","max","fit"]}],"font-size":[{text:["base",_,P]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",N]}],"font-family":[{font:[q]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",L]}],"line-clamp":[{"line-clamp":["none",j,N]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",T,L]}],"list-image":[{"list-image":["none",L]}],"list-style-type":[{list:["none","disc","decimal",L]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Y(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",T,P]}],"underline-offset":[{"underline-offset":["auto",T,L]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:z()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...V(),I]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",F]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},H]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[h]}],"gradient-via-pos":[{via:[h]}],"gradient-to-pos":[{to:[h]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...Y(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:Y()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...Y()]}],"outline-offset":[{"outline-offset":[T,L]}],"outline-w":[{outline:[T,P]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[T,P]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",_,B]}],"shadow-color":[{shadow:[q]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...U(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":U()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",_,L]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[b]}],sepia:[{sepia:[k]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[k]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",L]}],duration:[{duration:Q()}],ease:[{ease:["linear","in","out","in-out",L]}],delay:[{delay:Q()}],animate:[{animate:["none","spin","ping","pulse","bounce",L]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[O,L]}],"translate-x":[{"translate-x":[E]}],"translate-y":[{"translate-y":[E]}],"skew-x":[{"skew-x":[S]}],"skew-y":[{"skew-y":[S]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",L]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":z()}],"scroll-mx":[{"scroll-mx":z()}],"scroll-my":[{"scroll-my":z()}],"scroll-ms":[{"scroll-ms":z()}],"scroll-me":[{"scroll-me":z()}],"scroll-mt":[{"scroll-mt":z()}],"scroll-mr":[{"scroll-mr":z()}],"scroll-mb":[{"scroll-mb":z()}],"scroll-ml":[{"scroll-ml":z()}],"scroll-p":[{"scroll-p":z()}],"scroll-px":[{"scroll-px":z()}],"scroll-py":[{"scroll-py":z()}],"scroll-ps":[{"scroll-ps":z()}],"scroll-pe":[{"scroll-pe":z()}],"scroll-pt":[{"scroll-pt":z()}],"scroll-pr":[{"scroll-pr":z()}],"scroll-pb":[{"scroll-pb":z()}],"scroll-pl":[{"scroll-pl":z()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[T,P,N]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},9708:(e,t,n)=>{"use strict";n.d(t,{DX:()=>a,xV:()=>s});var r=n(2115),o=n(6101),i=n(5155),a=r.forwardRef((e,t)=>{let{children:n,...o}=e,a=r.Children.toArray(n),s=a.find(u);if(s){let e=s.props.children,n=a.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(l,{...o,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,i.jsx)(l,{...o,ref:t,children:n})});a.displayName="Slot";var l=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n);return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props),ref:t?(0,o.t)(t,e):e})}return r.Children.count(n)>1?r.Children.only(null):null});l.displayName="SlotClone";var s=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});function u(e){return r.isValidElement(e)&&e.type===s}},9946:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(2115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:s,className:u="",children:c,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...a,width:o,height:o,stroke:n,strokeWidth:s?24*Number(l)/Number(o):l,className:i("lucide",u),...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),s=(e,t)=>{let n=(0,r.forwardRef)((n,a)=>{let{className:s,...u}=n;return(0,r.createElement)(l,{ref:a,iconNode:t,className:i("lucide-".concat(o(e)),s),...u})});return n.displayName="".concat(e),n}}}]);
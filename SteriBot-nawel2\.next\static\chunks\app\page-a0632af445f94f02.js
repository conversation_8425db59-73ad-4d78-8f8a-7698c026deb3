(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1534:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>ao});var s=a(5155),i=a(2115),r=a(9708),n=a(2085),o=a(2432),l=a(2596),d=a(9688);function c(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,d.QP)((0,l.$)(t))}let m=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=i.forwardRef((e,t)=>{let{className:a,variant:i,size:n,asChild:o=!1,...l}=e,d=o?r.DX:"button";return(0,s.jsx)(d,{className:c(m({variant:i,size:n,className:a})),ref:t,...l})});u.displayName="Button";let x=i.forwardRef((e,t)=>{let{className:a,type:i,...r}=e;return(0,s.jsx)("input",{type:i,className:c("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...r})});x.displayName="Input";var h=a(7489);let g=i.forwardRef((e,t)=>{let{className:a,orientation:i="horizontal",decorative:r=!0,...n}=e;return(0,s.jsx)(h.b,{ref:t,decorative:r,orientation:i,className:c("shrink-0 bg-border","horizontal"===i?"h-[1px] w-full":"h-full w-[1px]",a),...n})});g.displayName=h.b.displayName;var p=a(5452),b=a(4416);let f=p.bL;p.l9,p.bm;let v=p.ZL,j=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(p.hJ,{className:c("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...i,ref:t})});j.displayName=p.hJ.displayName;let y=(0,n.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),N=i.forwardRef((e,t)=>{let{side:a="right",className:i,children:r,...n}=e;return(0,s.jsxs)(v,{children:[(0,s.jsx)(j,{}),(0,s.jsxs)(p.UC,{ref:t,className:c(y({side:a}),i),...n,children:[r,(0,s.jsxs)(p.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,s.jsx)(b.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});function w(e){let{className:t,...a}=e;return(0,s.jsx)("div",{className:c("animate-pulse rounded-md bg-muted",t),...a})}N.displayName=p.UC.displayName,i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(p.hE,{ref:t,className:c("text-lg font-semibold text-foreground",a),...i})}).displayName=p.hE.displayName,i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(p.VY,{ref:t,className:c("text-sm text-muted-foreground",a),...i})}).displayName=p.VY.displayName;var S=a(9613);let C=S.Kq,A=S.bL,R=S.l9,k=i.forwardRef((e,t)=>{let{className:a,sideOffset:i=4,...r}=e;return(0,s.jsx)(S.UC,{ref:t,sideOffset:i,className:c("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...r})});k.displayName=S.UC.displayName;let D=i.createContext(null);function T(){let e=i.useContext(D);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}let z=i.forwardRef((e,t)=>{let{defaultOpen:a=!0,open:r,onOpenChange:n,className:o,style:l,children:d,...m}=e,u=function(){let[e,t]=i.useState(void 0);return i.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),a=()=>{t(window.innerWidth<768)};return e.addEventListener("change",a),t(window.innerWidth<768),()=>e.removeEventListener("change",a)},[]),!!e}(),[x,h]=i.useState(!1),[g,p]=i.useState(a),b=null!=r?r:g,f=i.useCallback(e=>{let t="function"==typeof e?e(b):e;n?n(t):p(t),document.cookie="".concat("sidebar:state","=").concat(t,"; path=/; max-age=").concat(604800)},[n,b]),v=i.useCallback(()=>u?h(e=>!e):f(e=>!e),[u,f,h]);i.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),v())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[v]);let j=b?"expanded":"collapsed",y=i.useMemo(()=>({state:j,open:b,setOpen:f,isMobile:u,openMobile:x,setOpenMobile:h,toggleSidebar:v}),[j,b,f,u,x,h,v]);return(0,s.jsx)(D.Provider,{value:y,children:(0,s.jsx)(C,{delayDuration:0,children:(0,s.jsx)("div",{style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...l},className:c("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",o),ref:t,...m,children:d})})})});z.displayName="SidebarProvider";let E=i.forwardRef((e,t)=>{let{side:a="left",variant:i="sidebar",collapsible:r="offcanvas",className:n,children:o,...l}=e,{isMobile:d,state:m,openMobile:u,setOpenMobile:x}=T();return"none"===r?(0,s.jsx)("div",{className:c("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",n),ref:t,...l,children:o}):d?(0,s.jsx)(f,{open:u,onOpenChange:x,...l,children:(0,s.jsx)(N,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:a,children:(0,s.jsx)("div",{className:"flex h-full w-full flex-col",children:o})})}):(0,s.jsxs)("div",{ref:t,className:"group peer hidden md:block text-sidebar-foreground","data-state":m,"data-collapsible":"collapsed"===m?r:"","data-variant":i,"data-side":a,children:[(0,s.jsx)("div",{className:c("duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===i||"inset"===i?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),(0,s.jsx)("div",{className:c("duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex","left"===a?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===i||"inset"===i?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...l,children:(0,s.jsx)("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:o})})]})});E.displayName="Sidebar",i.forwardRef((e,t)=>{let{className:a,onClick:i,...r}=e,{toggleSidebar:n}=T();return(0,s.jsxs)(u,{ref:t,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:c("h-7 w-7",a),onClick:e=>{null==i||i(e),n()},...r,children:[(0,s.jsx)(o.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}).displayName="SidebarTrigger";let L=i.forwardRef((e,t)=>{let{className:a,...i}=e,{toggleSidebar:r}=T();return(0,s.jsx)("button",{ref:t,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:c("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",a),...i})});L.displayName="SidebarRail";let F=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("main",{ref:t,className:c("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",a),...i})});F.displayName="SidebarInset",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(x,{ref:t,"data-sidebar":"input",className:c("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",a),...i})}).displayName="SidebarInput";let M=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("div",{ref:t,"data-sidebar":"header",className:c("flex flex-col gap-2 p-2",a),...i})});M.displayName="SidebarHeader",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("div",{ref:t,"data-sidebar":"footer",className:c("flex flex-col gap-2 p-2",a),...i})}).displayName="SidebarFooter",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(g,{ref:t,"data-sidebar":"separator",className:c("mx-2 w-auto bg-sidebar-border",a),...i})}).displayName="SidebarSeparator";let O=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("div",{ref:t,"data-sidebar":"content",className:c("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",a),...i})});O.displayName="SidebarContent";let I=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("div",{ref:t,"data-sidebar":"group",className:c("relative flex w-full min-w-0 flex-col p-2",a),...i})});I.displayName="SidebarGroup",i.forwardRef((e,t)=>{let{className:a,asChild:i=!1,...n}=e,o=i?r.DX:"div";return(0,s.jsx)(o,{ref:t,"data-sidebar":"group-label",className:c("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",a),...n})}).displayName="SidebarGroupLabel",i.forwardRef((e,t)=>{let{className:a,asChild:i=!1,...n}=e,o=i?r.DX:"button";return(0,s.jsx)(o,{ref:t,"data-sidebar":"group-action",className:c("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",a),...n})}).displayName="SidebarGroupAction";let P=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("div",{ref:t,"data-sidebar":"group-content",className:c("w-full text-sm",a),...i})});P.displayName="SidebarGroupContent";let H=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("ul",{ref:t,"data-sidebar":"menu",className:c("flex w-full min-w-0 flex-col gap-1",a),...i})});H.displayName="SidebarMenu";let B=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("li",{ref:t,"data-sidebar":"menu-item",className:c("group/menu-item relative",a),...i})});B.displayName="SidebarMenuItem";let V=(0,n.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}}),q=i.forwardRef((e,t)=>{let{asChild:a=!1,isActive:i=!1,variant:n="default",size:o="default",tooltip:l,className:d,...m}=e,u=a?r.DX:"button",{isMobile:x,state:h}=T(),g=(0,s.jsx)(u,{ref:t,"data-sidebar":"menu-button","data-size":o,"data-active":i,className:c(V({variant:n,size:o}),d),...m});return l?("string"==typeof l&&(l={children:l}),(0,s.jsxs)(A,{children:[(0,s.jsx)(R,{asChild:!0,children:g}),(0,s.jsx)(k,{side:"right",align:"center",hidden:"collapsed"!==h||x,...l})]})):g});q.displayName="SidebarMenuButton",i.forwardRef((e,t)=>{let{className:a,asChild:i=!1,showOnHover:n=!1,...o}=e,l=i?r.DX:"button";return(0,s.jsx)(l,{ref:t,"data-sidebar":"menu-action",className:c("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",n&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",a),...o})}).displayName="SidebarMenuAction",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("div",{ref:t,"data-sidebar":"menu-badge",className:c("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",a),...i})}).displayName="SidebarMenuBadge",i.forwardRef((e,t)=>{let{className:a,showIcon:r=!1,...n}=e,o=i.useMemo(()=>"".concat(Math.floor(40*Math.random())+50,"%"),[]);return(0,s.jsxs)("div",{ref:t,"data-sidebar":"menu-skeleton",className:c("rounded-md h-8 flex gap-2 px-2 items-center",a),...n,children:[r&&(0,s.jsx)(w,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,s.jsx)(w,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":o}})]})}).displayName="SidebarMenuSkeleton",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("ul",{ref:t,"data-sidebar":"menu-sub",className:c("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",a),...i})}).displayName="SidebarMenuSub",i.forwardRef((e,t)=>{let{...a}=e;return(0,s.jsx)("li",{ref:t,...a})}).displayName="SidebarMenuSubItem",i.forwardRef((e,t)=>{let{asChild:a=!1,size:i="md",isActive:n,className:o,...l}=e,d=a?r.DX:"a";return(0,s.jsx)(d,{ref:t,"data-sidebar":"menu-sub-button","data-size":i,"data-active":n,className:c("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===i&&"text-xs","md"===i&&"text-sm","group-data-[collapsible=icon]:hidden",o),...l})}).displayName="SidebarMenuSubButton";var U=a(7340),J=a(2713),_=a(5657),W=a(5968),Z=a(9397),G=a(1243),Y=a(1007);let Q=[{code:"en",name:"English",nativeName:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8",rtl:!1},{code:"fr",name:"French",nativeName:"Fran\xe7ais",flag:"\uD83C\uDDEB\uD83C\uDDF7",rtl:!1},{code:"ar",name:"Arabic",nativeName:"العربية",flag:"\uD83C\uDDF8\uD83C\uDDE6",rtl:!0},{code:"es",name:"Spanish",nativeName:"Espa\xf1ol",flag:"\uD83C\uDDEA\uD83C\uDDF8",rtl:!1}],X={en:{login:{title:"SteriBOT",subtitle:"STERILISER",welcome:"Welcome back! Please sign in to your account.",email:"Email",emailPlaceholder:"Enter your email address",password:"Password",passwordPlaceholder:"Enter your password",connect:"Connect",connecting:"Connecting...",forgotPassword:"Forgot Password?",emailRequired:"Email is required",emailInvalid:"Please enter a valid email address",passwordRequired:"Password is required",passwordMinLength:"Password must be at least 6 characters"},network:{title:"Connect with",subtitle:"Please connect to a WiFi network or enable your mobile data (4G/5G)",wifi:"Connect with WiFi",wifiDescription:"Use wireless network connection",mobile:"Connect with Mobile Data",mobileDescription:"Use cellular network (4G/5G)",connect:"Connect",connecting:"Connecting..."},connecting:{title:"SteriBOT",subtitle:"Connecting robot",steps:{initializing:"Initializing connection...",scanning:"Scanning for devices...",establishing:"Establishing secure link...",synchronizing:"Synchronizing data...",established:"Connection established!"},complete:"% Complete",lookingFor:"Looking for your smart device",ensureDevice:"Please ensure your SteriBOT device is powered on and within range",wifi:"WiFi",mobileData:"Mobile Data"},robot:{title:"SteriBOT",subtitle:"Autonomous disinfection robot",connected:"Connected",active:"Active",startScan:"Start Scan"},navigation:{home:"Home",dashboard:"Dashboard",robotDetails:"Robot Details",robotsList:"Robots List",sterilizationHistory:"Sterilization History",obstacleDetection:"Obstacle Detection",profile:"Profile",logout:"Logout"},home:{title:"SteriBOT Control Center",subtitle:"Robot Management System",configurationScanning:"Configuration / Scanning space",robotStatus:"Robot Status",parameters:"Parameters",mapView:"Map View",cameraView:"Camera View",directionControls:"Direction Controls",reset:"Reset",startSterilization:"Start Sterilization",spaceConfiguration:"Space Configuration",sterilizationConfig:"Sterilization Configuration",position:"Position",battery:"Battery",speed:"Speed",resolution:"Resolution",scanRange:"Scan Range",updateRate:"Update Rate",stopScan:"Stop Scan",saveMap:"Save the Map",mapSaved:"Map Saved!",resetMap:"Reset Map"},dashboard:{title:"Dashboard",overview:"System Overview",statistics:"Statistics",activeConnections:"Active Connections",totalUsers:"Total Users",sterilizedAreas:"Sterilized Areas",efficiency:"Efficiency",bacteriaDetection:"Bacteria Detection",recentSessions:"Recent Sessions",sessionHistory:"Session History",date:"Date",duration:"Duration",area:"Area",status:"Status",completed:"Completed",inProgress:"In Progress",failed:"Failed",todaysConnections:"Today's Connections",todaysUsers:"Today's Users",sterilizedZones:"Sterilized Zones",fromSterilizedZones:"from 100 sterilized zones",operatingRoomA:"Operating Room A",surgeryRoom:"Surgery Room",mainCorridor:"Main Corridor",operatingRoomB:"Operating Room B",log:"Log",others:"Others",bacteriaTypes:{eColi:"E. coli",staphylococcus:"Staphylococcus",pseudomonas:"Pseudomonas",others:"Others"},bacteriaDetected24h:"Bacteria Detected (24h)",total:"Total",zone:"Zone",sterilizationLogChart:"Sterilization log chart",dateTime:"Date/Time",logStatus:"Log Status",firstLog:"1st Log",secondLog:"2nd Log",thirdLog:"3rd Log",fourthLog:"4th Log",fifthLog:"5th Log",sixthLog:"6th Log",activeRobots:"Active Robots",totalRobots:"Total Robots",criticalAlerts:"Critical Alerts"},robotDetails:{title:"Robot Details",information:"Robot Information",model:"Model",serialNumber:"Serial Number",status:"Status",batteryLevel:"Battery Level",lastMaintenance:"Last Maintenance",operatingHours:"Operating Hours",specifications:"Specifications",dimensions:"Dimensions",weight:"Weight",maxSpeed:"Max Speed",uvLamps:"UV Lamps",coverage:"Coverage Area",maintenance:"Maintenance",schedule:"Schedule",nextService:"Next Service",serviceHistory:"Service History",weekDays:{mon:"Mon",tue:"Tue",wed:"Wed",thu:"Thu",fri:"Fri",sat:"Sat",sun:"Sun"},months:{january:"January",february:"February",march:"March",april:"April",may:"May",june:"June",july:"July",august:"August",september:"September",october:"October",november:"November",december:"December"},scheduledTasks:"Scheduled Tasks",operatingRoomSterilization:"Operating Room A Sterilization",robotMaintenanceCheck:"Robot Maintenance Check",preSurgeryProtocol:"Pre-surgery sterilization protocol",weeklyMaintenance:"Weekly maintenance routine",scheduled:"Scheduled",completed:"Completed",inProgress:"In Progress",high:"High",medium:"Medium",low:"Low",sterilization:"Sterilization",maintenanceType:"Maintenance",operatingRoomA:"Operating Room A",robotStation:"Robot Station",drSmith:"Dr. Smith",techTeam:"Tech Team",mainCorridorCleaning:"Main Corridor Cleaning",emergencyRoomSterilization:"Emergency Room Sterilization",dailyCorridorSterilization:"Daily corridor sterilization",postIncidentSterilization:"Post-incident sterilization",mainCorridor:"Main Corridor",emergencyRoom:"Emergency Room",nightShift:"Night Shift",drJohnson:"Dr. Johnson",showCalendar:"Show Calendar",hideCalendar:"Hide Calendar",edit:"Edit",day:"Day",week:"Week",month:"Month",year:"Year",addTask:"Add Task",taskTitle:"Task Title",date:"Date",time:"Time",duration:"Duration (minutes)",priority:"Priority",roomLocation:"Room/Location",assignedTo:"Assigned To",notes:"Notes",create:"Create",cancel:"Cancel",editTask:"Edit Task",deleteTask:"Delete Task",save:"Save",minutes:"minutes"},robotsList:{title:"Robots List",addRobot:"Add Robot",addSpace:"Add Space",robotDescription:"Autonomous disinfection robot",startSterilization:"Start sterilization",name:"Name",location:"Location",description:"Description",roomType:"Room Type",area:"Area",addRobotTitle:"Add New Robot",addSpaceTitle:"Add New Space",cancel:"Cancel",add:"Add",addNewRobot:"Add new robot",robotName:"Robot Name",enterRobotName:"Enter robot name",selectModel:"Select model",serialNumber:"Serial Number",enterSerialNumber:"Enter serial number",enterLocation:"Enter location",enterDescription:"Enter description",addNewSpace:"Add new space",spaceName:"Space Name",enterSpaceName:"Enter space name",selectRoomType:"Select room type",enterArea:"Enter area",operatingRoom:"Operating Room",surgeryRoom:"Surgery Room",corridor:"Corridor",laboratory:"Laboratory"},profile:{title:"Profile",personalInfo:"Personal Information",name:"Name",email:"Email",role:"Role",department:"Department",phone:"Phone",settings:"Settings",notifications:"Notifications",language:"Language",timezone:"Timezone",theme:"Theme",security:"Security",changePassword:"Change Password",twoFactor:"Two-Factor Authentication",loginHistory:"Login History",editProfile:"Edit Profile",accountStatus:"Account Status",memberSince:"Member Since",lastLogin:"Last Login",accessLevel:"Access Level",administrator:"Administrator",details:"Details",lastName:"Last Name",jobTitle:"Job Title",doctor:"Doctor",cardiology:"Cardiology",securitySettings:"Security Settings",downloadData:"Download Data",notifications2FA:"Notifications & 2FA",logoutAllDevices:"Logout All Devices",today:"Today",january2024:"January 2024"},sterilizationHistory:{title:"Sterilization History",history:"History",filter:"Filter",dateRange:"Date Range",robotFilter:"Robot Filter",statusFilter:"Status Filter",export:"Export",details:"Details",startTime:"Start Time",endTime:"End Time",coverage:"Coverage",effectiveness:"Effectiveness",robotDescription:"Autonomous disinfection robot",activityHistory:"Activity History",bacteria:"Bacteria",staphAureus:"Staph. aureus",log:"Log",log4:"Log 4",operationsHistory:"Operations History",id:"ID",zone:"Zone",duration:"Duration",status:"Status",efficiency:"Efficiency",operationDetails:"Operation Details",operationId:"Operation ID",resolve:"Resolve",close:"Close",all:"All",completed:"Completed",inProgress:"In Progress",failed:"Failed",exportData:"Export Data",refresh:"Refresh"},obstacleDetection:{title:"Obstacle Detection",detection:"Detection System",sensors:"Sensors",alerts:"Alerts",sensitivity:"Sensitivity",calibration:"Calibration",testMode:"Test Mode",sensorStatus:"Sensor Status",frontSensor:"Front Sensor",rearSensor:"Rear Sensor",sideSensors:"Side Sensors",operational:"Operational",warning:"Warning",error:"Error",allTypes:"All Types",connection:"Connection",battery:"Battery",human:"Human",allSeverities:"All Severities",high:"High",medium:"Medium",low:"Low",severity:"Severity",type:"Type",time:"Time",location:"Location",action:"Action",resolve:"Resolve",alertDetails:"Alert Details",alertId:"Alert ID",description:"Description",resolveAlert:"Resolve Alert",resolutionNote:"Resolution Note",enterNote:"Enter resolution note",markResolved:"Mark as Resolved",cancel:"Cancel",close:"Close",robotStopped:"Robot stopped due to obstacle",lowBattery:"Low battery warning",humanDetected:"Human detected in sterilization zone",connectionLost:"Connection lost with robot",resolved:"Resolved",pending:"Pending",critical:"Critical",info:"Info"},modals:{spaceConfig:{title:"Space Configuration",roomType:"Room Type",dimensions:"Dimensions",obstacles:"Obstacles",specialRequirements:"Special Requirements",save:"Save Configuration",cancel:"Cancel"},sterilizationConfig:{title:"Sterilization Configuration",mode:"Sterilization Mode",intensity:"UV Intensity",duration:"Duration",schedule:"Schedule",start:"Start Sterilization",cancel:"Cancel"}},status:{online:"Online",offline:"Offline",charging:"Charging",working:"Working",idle:"Idle",maintenance:"Maintenance",error:"Error",connected:"Connected",disconnected:"Disconnected",active:"Active",inactive:"Inactive",resolved:"Resolved"},common:{cancel:"Cancel",confirm:"Confirm",close:"Close",save:"Save",edit:"Edit",delete:"Delete",add:"Add",remove:"Remove",loading:"Loading...",error:"Error",success:"Success",warning:"Warning",info:"Information",yes:"Yes",no:"No",ok:"OK",back:"Back",next:"Next",previous:"Previous",finish:"Finish",retry:"Retry",refresh:"Refresh"},errors:{networkError:"Network error - please check your connection",timeout:"Request timeout - please try again",authRequired:"Authentication required - please log in",accessDenied:"Access denied - insufficient permissions",notFound:"Resource not found",serverError:"Server error - please try again later",invalidData:"Invalid data provided",connectionFailed:"Connection failed",loadingFailed:"Failed to load data",saveFailed:"Failed to save changes",deleteFailed:"Failed to delete item",updateFailed:"Failed to update item",exportFailed:"Failed to export data",importFailed:"Failed to import data",validationError:"Validation error",unknownError:"An unknown error occurred"},loading:{general:"Loading...",robots:"Loading robots...",sessions:"Loading sessions...",alerts:"Loading alerts...",dashboard:"Loading dashboard...",saving:"Saving...",deleting:"Deleting...",updating:"Updating...",exporting:"Exporting...",importing:"Importing...",connecting:"Connecting..."}},fr:{login:{title:"SteriBOT",subtitle:"ST\xc9RILISATEUR",welcome:"Bon retour ! Veuillez vous connecter \xe0 votre compte.",email:"Email",emailPlaceholder:"Entrez votre adresse email",password:"Mot de passe",passwordPlaceholder:"Entrez votre mot de passe",connect:"Se connecter",connecting:"Connexion...",forgotPassword:"Mot de passe oubli\xe9 ?",emailRequired:"L'email est requis",emailInvalid:"Veuillez entrer une adresse email valide",passwordRequired:"Le mot de passe est requis",passwordMinLength:"Le mot de passe doit contenir au moins 6 caract\xe8res"},network:{title:"Se connecter avec",subtitle:"Veuillez vous connecter \xe0 un r\xe9seau WiFi ou activer vos donn\xe9es mobiles (4G/5G)",wifi:"Se connecter avec WiFi",wifiDescription:"Utiliser la connexion r\xe9seau sans fil",mobile:"Se connecter avec les donn\xe9es mobiles",mobileDescription:"Utiliser le r\xe9seau cellulaire (4G/5G)",connect:"Se connecter",connecting:"Connexion..."},connecting:{title:"SteriBOT",subtitle:"Connexion du robot",steps:{initializing:"Initialisation de la connexion...",scanning:"Recherche d'appareils...",establishing:"\xc9tablissement d'une liaison s\xe9curis\xe9e...",synchronizing:"Synchronisation des donn\xe9es...",established:"Connexion \xe9tablie !"},complete:"% Termin\xe9",lookingFor:"Recherche de votre appareil intelligent",ensureDevice:"Veuillez vous assurer que votre appareil SteriBOT est allum\xe9 et \xe0 port\xe9e",wifi:"WiFi",mobileData:"Donn\xe9es mobiles"},robot:{title:"SteriBOT",subtitle:"Robot de d\xe9sinfection autonome",connected:"Connect\xe9",active:"Actif",startScan:"D\xe9marrer le scan"},navigation:{home:"Accueil",dashboard:"Tableau de bord",robotDetails:"D\xe9tails du robot",robotsList:"Liste des robots",sterilizationHistory:"Historique de st\xe9rilisation",obstacleDetection:"D\xe9tection d'obstacles",profile:"Profil",logout:"D\xe9connexion"},home:{title:"Centre de contr\xf4le SteriBOT",subtitle:"Syst\xe8me de gestion des robots",configurationScanning:"Configuration / Scan de l'espace",robotStatus:"\xc9tat du robot",parameters:"Param\xe8tres",mapView:"Vue de la carte",cameraView:"Vue cam\xe9ra",directionControls:"Contr\xf4les de direction",reset:"R\xe9initialiser",startSterilization:"D\xe9marrer la st\xe9rilisation",spaceConfiguration:"Configuration de l'espace",sterilizationConfig:"Configuration de st\xe9rilisation",position:"Position",battery:"Batterie",speed:"Vitesse",resolution:"R\xe9solution",scanRange:"Port\xe9e de scan",updateRate:"Taux de mise \xe0 jour",stopScan:"Arr\xeater le scan",saveMap:"Sauvegarder la carte",mapSaved:"Carte sauvegard\xe9e !",resetMap:"R\xe9initialiser la carte"},dashboard:{title:"Tableau de bord",overview:"Vue d'ensemble du syst\xe8me",statistics:"Statistiques",activeConnections:"Connexions actives",totalUsers:"Utilisateurs totaux",sterilizedAreas:"Zones st\xe9rilis\xe9es",efficiency:"Efficacit\xe9",bacteriaDetection:"D\xe9tection de bact\xe9ries",recentSessions:"Sessions r\xe9centes",sessionHistory:"Historique des sessions",date:"Date",duration:"Dur\xe9e",area:"Zone",status:"\xc9tat",completed:"Termin\xe9",inProgress:"En cours",failed:"\xc9chou\xe9",todaysConnections:"Connexions d'aujourd'hui",todaysUsers:"Utilisateurs d'aujourd'hui",sterilizedZones:"Zones st\xe9rilis\xe9es",fromSterilizedZones:"sur 100 zones st\xe9rilis\xe9es",operatingRoomA:"Salle d'op\xe9ration A",surgeryRoom:"Salle de chirurgie",mainCorridor:"Couloir principal",operatingRoomB:"Salle d'op\xe9ration B",log:"Journal",others:"Autres",bacteriaTypes:{eColi:"E. coli",staphylococcus:"Staphylocoque",pseudomonas:"Pseudomonas",others:"Autres"},bacteriaDetected24h:"Bact\xe9ries d\xe9tect\xe9es (24h)",total:"Total",zone:"Zone",sterilizationLogChart:"Graphique des journaux de st\xe9rilisation",dateTime:"Date/Heure",logStatus:"\xc9tat du journal",firstLog:"1er Journal",secondLog:"2e Journal",thirdLog:"3e Journal",fourthLog:"4e Journal",fifthLog:"5e Journal",sixthLog:"6e Journal",activeRobots:"Robots Actifs",totalRobots:"Total Robots",criticalAlerts:"Alertes Critiques"},robotDetails:{title:"D\xe9tails du robot",information:"Informations du robot",model:"Mod\xe8le",serialNumber:"Num\xe9ro de s\xe9rie",status:"\xc9tat",batteryLevel:"Niveau de batterie",lastMaintenance:"Derni\xe8re maintenance",operatingHours:"Heures de fonctionnement",specifications:"Sp\xe9cifications",dimensions:"Dimensions",weight:"Poids",maxSpeed:"Vitesse maximale",uvLamps:"Lampes UV",coverage:"Zone de couverture",maintenance:"Maintenance",schedule:"Planification",nextService:"Prochain service",serviceHistory:"Historique de service",weekDays:{mon:"Lun",tue:"Mar",wed:"Mer",thu:"Jeu",fri:"Ven",sat:"Sam",sun:"Dim"},months:{january:"Janvier",february:"F\xe9vrier",march:"Mars",april:"Avril",may:"Mai",june:"Juin",july:"Juillet",august:"Ao\xfbt",september:"Septembre",october:"Octobre",november:"Novembre",december:"D\xe9cembre"},scheduledTasks:"T\xe2ches programm\xe9es",operatingRoomSterilization:"St\xe9rilisation salle d'op\xe9ration A",robotMaintenanceCheck:"V\xe9rification maintenance robot",preSurgeryProtocol:"Protocole de st\xe9rilisation pr\xe9-chirurgical",weeklyMaintenance:"Routine de maintenance hebdomadaire",scheduled:"Programm\xe9",completed:"Termin\xe9",inProgress:"En cours",high:"\xc9lev\xe9",medium:"Moyen",low:"Faible",sterilization:"St\xe9rilisation",maintenanceType:"Maintenance",operatingRoomA:"Salle d'op\xe9ration A",robotStation:"Station robot",drSmith:"Dr. Smith",techTeam:"\xc9quipe technique",mainCorridorCleaning:"Nettoyage couloir principal",emergencyRoomSterilization:"St\xe9rilisation salle d'urgence",dailyCorridorSterilization:"St\xe9rilisation quotidienne du couloir",postIncidentSterilization:"St\xe9rilisation post-incident",mainCorridor:"Couloir principal",emergencyRoom:"Salle d'urgence",nightShift:"\xc9quipe de nuit",drJohnson:"Dr. Johnson",showCalendar:"Afficher le calendrier",hideCalendar:"Masquer le calendrier",edit:"Modifier",day:"Jour",week:"Semaine",month:"Mois",year:"Ann\xe9e",addTask:"Ajouter une t\xe2che",taskTitle:"Titre de la t\xe2che",date:"Date",time:"Heure",duration:"Dur\xe9e (minutes)",priority:"Priorit\xe9",roomLocation:"Salle/Emplacement",assignedTo:"Assign\xe9 \xe0",notes:"Notes",create:"Cr\xe9er",cancel:"Annuler",editTask:"Modifier la t\xe2che",deleteTask:"Supprimer la t\xe2che",save:"Enregistrer",minutes:"minutes"},robotsList:{title:"Liste des robots",addRobot:"Ajouter un robot",addSpace:"Ajouter un espace",robotDescription:"Robot de d\xe9sinfection autonome",startSterilization:"D\xe9marrer la st\xe9rilisation",name:"Nom",location:"Emplacement",description:"Description",roomType:"Type de pi\xe8ce",area:"Zone",addRobotTitle:"Ajouter un nouveau robot",addSpaceTitle:"Ajouter un nouvel espace",cancel:"Annuler",add:"Ajouter",addNewRobot:"Ajouter un nouveau robot",robotName:"Nom du robot",enterRobotName:"Entrer le nom du robot",selectModel:"S\xe9lectionner le mod\xe8le",serialNumber:"Num\xe9ro de s\xe9rie",enterSerialNumber:"Entrer le num\xe9ro de s\xe9rie",enterLocation:"Entrer l'emplacement",enterDescription:"Entrer la description",addNewSpace:"Ajouter un nouvel espace",spaceName:"Nom de l'espace",enterSpaceName:"Entrer le nom de l'espace",selectRoomType:"S\xe9lectionner le type de pi\xe8ce",enterArea:"Entrer la zone",operatingRoom:"Salle d'op\xe9ration",surgeryRoom:"Salle de chirurgie",corridor:"Couloir",laboratory:"Laboratoire"},profile:{title:"Profil",personalInfo:"Informations personnelles",name:"Nom",email:"Email",role:"R\xf4le",department:"D\xe9partement",phone:"T\xe9l\xe9phone",settings:"Param\xe8tres",notifications:"Notifications",language:"Langue",timezone:"Fuseau horaire",theme:"Th\xe8me",security:"S\xe9curit\xe9",changePassword:"Changer le mot de passe",twoFactor:"Authentification \xe0 deux facteurs",loginHistory:"Historique de connexion",editProfile:"Modifier le profil",accountStatus:"\xc9tat du compte",memberSince:"Membre depuis",lastLogin:"Derni\xe8re connexion",accessLevel:"Niveau d'acc\xe8s",administrator:"Administrateur",details:"D\xe9tails",lastName:"Nom de famille",jobTitle:"Titre du poste",doctor:"Docteur",cardiology:"Cardiologie",securitySettings:"Actions rapides",downloadData:"T\xe9l\xe9charger le rapport d'activit\xe9",notifications2FA:"Param\xe8tres de notification",logoutAllDevices:"Se d\xe9connecter",today:"Aujourd'hui",january2024:"Janvier 2024"},sterilizationHistory:{title:"Historique de st\xe9rilisation",history:"Historique",filter:"Filtrer",dateRange:"Plage de dates",robotFilter:"Filtre robot",statusFilter:"Filtre d'\xe9tat",export:"Exporter",details:"D\xe9tails",startTime:"Heure de d\xe9but",endTime:"Heure de fin",coverage:"Couverture",effectiveness:"Efficacit\xe9",robotDescription:"Robot de d\xe9sinfection autonome",activityHistory:"Historique d'activit\xe9",bacteria:"Bact\xe9rie",staphAureus:"Staph. aureus",log:"Journal",log4:"Journal 4",operationsHistory:"Historique des op\xe9rations",id:"ID",zone:"Zone",duration:"Dur\xe9e",status:"\xc9tat",efficiency:"Efficacit\xe9",operationDetails:"D\xe9tails de l'op\xe9ration",operationId:"ID de l'op\xe9ration",resolve:"R\xe9soudre",close:"Fermer",all:"Tous",completed:"Termin\xe9",inProgress:"En cours",failed:"Incident",exportData:"Exporter les donn\xe9es",refresh:"Actualiser"},obstacleDetection:{title:"D\xe9tection d'obstacles",detection:"Syst\xe8me de d\xe9tection",sensors:"Capteurs",alerts:"Alertes",sensitivity:"Sensibilit\xe9",calibration:"Calibrage",testMode:"Mode test",sensorStatus:"\xc9tat des capteurs",frontSensor:"Capteur avant",rearSensor:"Capteur arri\xe8re",sideSensors:"Capteurs lat\xe9raux",operational:"Op\xe9rationnel",warning:"Avertissement",error:"Erreur",allTypes:"Tous les types",connection:"Connexion",battery:"Batterie",human:"Humain",allSeverities:"Toutes les s\xe9v\xe9rit\xe9s",high:"\xc9lev\xe9",medium:"Moyen",low:"Faible",severity:"S\xe9v\xe9rit\xe9",type:"Type",time:"Heure",location:"Emplacement",action:"Action",resolve:"R\xe9soudre",alertDetails:"D\xe9tails de l'alerte",alertId:"ID de l'alerte",description:"Description",resolveAlert:"R\xe9soudre l'alerte",resolutionNote:"Note de r\xe9solution",enterNote:"Entrer une note de r\xe9solution",markResolved:"Marquer comme r\xe9solu",cancel:"Annuler",close:"Fermer",robotStopped:"Robot arr\xeat\xe9 \xe0 cause d'un obstacle",lowBattery:"Avertissement de batterie faible",humanDetected:"Humain d\xe9tect\xe9 dans la zone de st\xe9rilisation",connectionLost:"Connexion perdue avec le robot",resolved:"R\xe9solu",pending:"En attente",critical:"Critique",info:"Info"},modals:{spaceConfig:{title:"Configuration de l'espace",roomType:"Type de pi\xe8ce",dimensions:"Dimensions",obstacles:"Obstacles",specialRequirements:"Exigences sp\xe9ciales",save:"Enregistrer la configuration",cancel:"Annuler"},sterilizationConfig:{title:"Configuration de st\xe9rilisation",mode:"Mode de st\xe9rilisation",intensity:"Intensit\xe9 UV",duration:"Dur\xe9e",schedule:"Planification",start:"D\xe9marrer la st\xe9rilisation",cancel:"Annuler"}},status:{online:"En ligne",offline:"Hors ligne",charging:"En charge",working:"En fonctionnement",idle:"Inactif",maintenance:"Maintenance",error:"Erreur",connected:"Connect\xe9",disconnected:"D\xe9connect\xe9",active:"Actif",inactive:"Inactif",resolved:"R\xe9solu"},common:{cancel:"Annuler",confirm:"Confirmer",close:"Fermer",save:"Enregistrer",edit:"Modifier",delete:"Supprimer",add:"Ajouter",remove:"Retirer",loading:"Chargement...",error:"Erreur",success:"Succ\xe8s",warning:"Avertissement",info:"Information",yes:"Oui",no:"Non",ok:"OK",back:"Retour",next:"Suivant",previous:"Pr\xe9c\xe9dent",finish:"Terminer",retry:"R\xe9essayer",refresh:"Actualiser"},errors:{networkError:"Erreur r\xe9seau - veuillez v\xe9rifier votre connexion",timeout:"D\xe9lai d'attente d\xe9pass\xe9 - veuillez r\xe9essayer",authRequired:"Authentification requise - veuillez vous connecter",accessDenied:"Acc\xe8s refus\xe9 - permissions insuffisantes",notFound:"Ressource non trouv\xe9e",serverError:"Erreur serveur - veuillez r\xe9essayer plus tard",invalidData:"Donn\xe9es invalides fournies",connectionFailed:"\xc9chec de la connexion",loadingFailed:"\xc9chec du chargement des donn\xe9es",saveFailed:"\xc9chec de la sauvegarde",deleteFailed:"\xc9chec de la suppression",updateFailed:"\xc9chec de la mise \xe0 jour",exportFailed:"\xc9chec de l'exportation",importFailed:"\xc9chec de l'importation",validationError:"Erreur de validation",unknownError:"Une erreur inconnue s'est produite"},loading:{general:"Chargement...",robots:"Chargement des robots...",sessions:"Chargement des sessions...",alerts:"Chargement des alertes...",dashboard:"Chargement du tableau de bord...",saving:"Sauvegarde...",deleting:"Suppression...",updating:"Mise \xe0 jour...",exporting:"Exportation...",importing:"Importation...",connecting:"Connexion..."}},ar:{login:{title:"ستيريبوت",subtitle:"جهاز التعقيم",welcome:"مرحباً بعودتك! يرجى تسجيل الدخول إلى حسابك.",email:"البريد الإلكتروني",emailPlaceholder:"أدخل عنوان بريدك الإلكتروني",password:"كلمة المرور",passwordPlaceholder:"أدخل كلمة المرور",connect:"اتصال",connecting:"جاري الاتصال...",forgotPassword:"نسيت كلمة المرور؟",emailRequired:"البريد الإلكتروني مطلوب",emailInvalid:"يرجى إدخال عنوان بريد إلكتروني صحيح",passwordRequired:"كلمة المرور مطلوبة",passwordMinLength:"يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل"},network:{title:"الاتصال عبر",subtitle:"يرجى الاتصال بشبكة WiFi أو تفعيل بيانات الهاتف المحمول (4G/5G)",wifi:"الاتصال عبر WiFi",wifiDescription:"استخدام اتصال الشبكة اللاسلكية",mobile:"الاتصال عبر بيانات الهاتف",mobileDescription:"استخدام الشبكة الخلوية (4G/5G)",connect:"اتصال",connecting:"جاري الاتصال..."},connecting:{title:"ستيريبوت",subtitle:"ربط الروبوت",steps:{initializing:"تهيئة الاتصال...",scanning:"البحث عن الأجهزة...",establishing:"إنشاء رابط آمن...",synchronizing:"مزامنة البيانات...",established:"تم إنشاء الاتصال!"},complete:"% مكتمل",lookingFor:"البحث عن جهازك الذكي",ensureDevice:"يرجى التأكد من تشغيل جهاز ستيريبوت وأنه في النطاق",wifi:"واي فاي",mobileData:"بيانات الهاتف"},robot:{title:"ستيريبوت",subtitle:"روبوت التطهير المستقل",connected:"متصل",active:"نشط",startScan:"بدء المسح"},navigation:{home:"الرئيسية",dashboard:"لوحة التحكم",robotDetails:"تفاصيل الروبوت",robotsList:"قائمة الروبوتات",sterilizationHistory:"تاريخ التعقيم",obstacleDetection:"كشف العوائق",profile:"الملف الشخصي",logout:"تسجيل الخروج"},home:{title:"مركز التحكم ستيريبوت",subtitle:"نظام إدارة الروبوتات",configurationScanning:"التكوين / مسح المساحة",robotStatus:"حالة الروبوت",parameters:"المعاملات",mapView:"عرض الخريطة",cameraView:"عرض الكاميرا",directionControls:"عناصر التحكم في الاتجاه",reset:"إعادة تعيين",startSterilization:"بدء التعقيم",spaceConfiguration:"تكوين المساحة",sterilizationConfig:"تكوين التعقيم",position:"الموضع",battery:"البطارية",speed:"السرعة",resolution:"الدقة",scanRange:"نطاق المسح",updateRate:"معدل التحديث",stopScan:"إيقاف المسح",saveMap:"حفظ الخريطة",mapSaved:"تم حفظ الخريطة!",resetMap:"إعادة تعيين الخريطة"},dashboard:{title:"لوحة التحكم",overview:"نظرة عامة على النظام",statistics:"الإحصائيات",activeConnections:"الاتصالات النشطة",totalUsers:"إجمالي المستخدمين",sterilizedAreas:"المناطق المعقمة",efficiency:"الكفاءة",bacteriaDetection:"كشف البكتيريا",recentSessions:"الجلسات الأخيرة",sessionHistory:"تاريخ الجلسات",date:"التاريخ",duration:"المدة",area:"المنطقة",status:"الحالة",completed:"مكتمل",inProgress:"قيد التقدم",failed:"فشل",todaysConnections:"اتصالات اليوم",todaysUsers:"مستخدمو اليوم",sterilizedZones:"المناطق المعقمة",fromSterilizedZones:"من 100 منطقة معقمة",operatingRoomA:"غرفة العمليات أ",surgeryRoom:"غرفة الجراحة",mainCorridor:"الممر الرئيسي",operatingRoomB:"غرفة العمليات ب",log:"سجل",others:"أخرى",bacteriaTypes:{eColi:"الإشريكية القولونية",staphylococcus:"المكورات العنقودية",pseudomonas:"الزائفة",others:"أخرى"},bacteriaDetected24h:"البكتيريا المكتشفة (24 ساعة)",total:"المجموع",zone:"المنطقة",sterilizationLogChart:"مخطط سجلات التعقيم",dateTime:"التاريخ/الوقت",logStatus:"حالة السجل",firstLog:"السجل الأول",secondLog:"السجل الثاني",thirdLog:"السجل الثالث",fourthLog:"السجل الرابع",fifthLog:"السجل الخامس",sixthLog:"السجل السادس",activeRobots:"الروبوتات النشطة",totalRobots:"إجمالي الروبوتات",criticalAlerts:"التنبيهات الحرجة"},robotDetails:{title:"تفاصيل الروبوت",information:"معلومات الروبوت",model:"الطراز",serialNumber:"الرقم التسلسلي",status:"الحالة",batteryLevel:"مستوى البطارية",lastMaintenance:"آخر صيانة",operatingHours:"ساعات التشغيل",specifications:"المواصفات",dimensions:"الأبعاد",weight:"الوزن",maxSpeed:"السرعة القصوى",uvLamps:"مصابيح الأشعة فوق البنفسجية",coverage:"منطقة التغطية",maintenance:"الصيانة",schedule:"الجدولة",nextService:"الخدمة التالية",serviceHistory:"تاريخ الخدمة",weekDays:{mon:"الإثنين",tue:"الثلاثاء",wed:"الأربعاء",thu:"الخميس",fri:"الجمعة",sat:"السبت",sun:"الأحد"},months:{january:"يناير",february:"فبراير",march:"مارس",april:"أبريل",may:"مايو",june:"يونيو",july:"يوليو",august:"أغسطس",september:"سبتمبر",october:"أكتوبر",november:"نوفمبر",december:"ديسمبر"},scheduledTasks:"المهام المجدولة",operatingRoomSterilization:"تعقيم غرفة العمليات أ",robotMaintenanceCheck:"فحص صيانة الروبوت",preSurgeryProtocol:"بروتوكول التعقيم قبل الجراحة",weeklyMaintenance:"روتين الصيانة الأسبوعية",scheduled:"مجدول",completed:"مكتمل",inProgress:"قيد التقدم",high:"عالي",medium:"متوسط",low:"منخفض",sterilization:"التعقيم",maintenanceType:"الصيانة",operatingRoomA:"غرفة العمليات أ",robotStation:"محطة الروبوت",drSmith:"د. سميث",techTeam:"الفريق التقني",mainCorridorCleaning:"تنظيف الممر الرئيسي",emergencyRoomSterilization:"تعقيم غرفة الطوارئ",dailyCorridorSterilization:"تعقيم الممر اليومي",postIncidentSterilization:"تعقيم ما بعد الحادث",mainCorridor:"الممر الرئيسي",emergencyRoom:"غرفة الطوارئ",nightShift:"الوردية الليلية",drJohnson:"د. جونسون",showCalendar:"إظهار التقويم",hideCalendar:"إخفاء التقويم",edit:"تحرير",day:"يوم",week:"أسبوع",month:"شهر",year:"سنة",addTask:"إضافة مهمة",taskTitle:"عنوان المهمة",date:"التاريخ",time:"الوقت",duration:"المدة (بالدقائق)",priority:"الأولوية",roomLocation:"الغرفة/الموقع",assignedTo:"مُسند إلى",notes:"ملاحظات",create:"إنشاء",cancel:"إلغاء",editTask:"تحرير المهمة",deleteTask:"حذف المهمة",save:"حفظ",minutes:"دقائق"},robotsList:{title:"قائمة الروبوتات",addRobot:"إضافة روبوت",addSpace:"إضافة مساحة",robotDescription:"روبوت التطهير المستقل",startSterilization:"بدء التعقيم",name:"الاسم",location:"الموقع",description:"الوصف",roomType:"نوع الغرفة",area:"المنطقة",addRobotTitle:"إضافة روبوت جديد",addSpaceTitle:"إضافة مساحة جديدة",cancel:"إلغاء",add:"إضافة",addNewRobot:"إضافة روبوت جديد",robotName:"اسم الروبوت",enterRobotName:"أدخل اسم الروبوت",selectModel:"اختر الطراز",serialNumber:"الرقم التسلسلي",enterSerialNumber:"أدخل الرقم التسلسلي",enterLocation:"أدخل الموقع",enterDescription:"أدخل الوصف",addNewSpace:"إضافة مساحة جديدة",spaceName:"اسم المساحة",enterSpaceName:"أدخل اسم المساحة",selectRoomType:"اختر نوع الغرفة",enterArea:"أدخل المنطقة",operatingRoom:"غرفة العمليات",surgeryRoom:"غرفة الجراحة",corridor:"الممر",laboratory:"المختبر"},profile:{title:"الملف الشخصي",personalInfo:"المعلومات الشخصية",name:"الاسم",email:"البريد الإلكتروني",role:"الدور",department:"القسم",phone:"الهاتف",settings:"الإعدادات",notifications:"الإشعارات",language:"اللغة",timezone:"المنطقة الزمنية",theme:"المظهر",security:"الأمان",changePassword:"تغيير كلمة المرور",twoFactor:"المصادقة الثنائية",loginHistory:"تاريخ تسجيل الدخول",editProfile:"تعديل الملف الشخصي",accountStatus:"حالة الحساب",memberSince:"عضو منذ",lastLogin:"آخر تسجيل دخول",accessLevel:"مستوى الوصول",administrator:"مدير",details:"التفاصيل",lastName:"اسم العائلة",jobTitle:"المسمى الوظيفي",doctor:"طبيب",cardiology:"أمراض القلب",securitySettings:"إجراءات سريعة",downloadData:"تحميل تقرير النشاط",notifications2FA:"إعدادات الإشعارات",logoutAllDevices:"تسجيل الخروج",today:"اليوم",january2024:"يناير 2024"},sterilizationHistory:{title:"تاريخ التعقيم",history:"التاريخ",filter:"تصفية",dateRange:"نطاق التاريخ",robotFilter:"مرشح الروبوت",statusFilter:"مرشح الحالة",export:"تصدير",details:"التفاصيل",startTime:"وقت البداية",endTime:"وقت النهاية",coverage:"التغطية",effectiveness:"الفعالية",robotDescription:"روبوت التطهير المستقل",activityHistory:"تاريخ النشاط",bacteria:"البكتيريا",staphAureus:"المكورات العنقودية الذهبية",log:"سجل",log4:"سجل 4",operationsHistory:"تاريخ العمليات",id:"المعرف",zone:"المنطقة",duration:"المدة",status:"الحالة",efficiency:"الكفاءة",operationDetails:"تفاصيل العملية",operationId:"معرف العملية",resolve:"حل",close:"إغلاق",all:"الكل",completed:"مكتمل",inProgress:"قيد التقدم",failed:"حادث",exportData:"تصدير البيانات",refresh:"تحديث"},obstacleDetection:{title:"كشف العوائق",detection:"نظام الكشف",sensors:"أجهزة الاستشعار",alerts:"التنبيهات",sensitivity:"الحساسية",calibration:"المعايرة",testMode:"وضع الاختبار",sensorStatus:"حالة المستشعر",frontSensor:"المستشعر الأمامي",rearSensor:"المستشعر الخلفي",sideSensors:"المستشعرات الجانبية",operational:"تشغيلي",warning:"تحذير",error:"خطأ",allTypes:"جميع الأنواع",connection:"الاتصال",battery:"البطارية",human:"إنسان",allSeverities:"جميع مستويات الخطورة",high:"عالي",medium:"متوسط",low:"منخفض",severity:"الخطورة",type:"النوع",time:"الوقت",location:"الموقع",action:"الإجراء",resolve:"حل",alertDetails:"تفاصيل التنبيه",alertId:"معرف التنبيه",description:"الوصف",resolveAlert:"حل التنبيه",resolutionNote:"ملاحظة الحل",enterNote:"أدخل ملاحظة الحل",markResolved:"وضع علامة كمحلول",cancel:"إلغاء",close:"إغلاق",robotStopped:"توقف الروبوت بسبب عائق",lowBattery:"تحذير انخفاض البطارية",humanDetected:"تم اكتشاف إنسان في منطقة التعقيم",connectionLost:"فقدان الاتصال مع الروبوت",resolved:"محلول",pending:"معلق",critical:"حرج",info:"معلومات"},modals:{spaceConfig:{title:"تكوين المساحة",roomType:"نوع الغرفة",dimensions:"الأبعاد",obstacles:"العوائق",specialRequirements:"المتطلبات الخاصة",save:"حفظ التكوين",cancel:"إلغاء"},sterilizationConfig:{title:"تكوين التعقيم",mode:"وضع التعقيم",intensity:"شدة الأشعة فوق البنفسجية",duration:"المدة",schedule:"الجدولة",start:"بدء التعقيم",cancel:"إلغاء"}},status:{online:"متصل",offline:"غير متصل",charging:"يشحن",working:"يعمل",idle:"خامل",maintenance:"صيانة",error:"خطأ",connected:"متصل",disconnected:"منقطع",active:"نشط",inactive:"غير نشط",resolved:"محلول"},common:{cancel:"إلغاء",confirm:"تأكيد",close:"إغلاق",save:"حفظ",edit:"تحرير",delete:"حذف",add:"إضافة",remove:"إزالة",loading:"جاري التحميل...",error:"خطأ",success:"نجح",warning:"تحذير",info:"معلومات",yes:"نعم",no:"لا",ok:"موافق",back:"رجوع",next:"التالي",previous:"السابق",finish:"إنهاء",retry:"إعادة المحاولة",refresh:"تحديث"},errors:{networkError:"خطأ في الشبكة - يرجى التحقق من اتصالك",timeout:"انتهت مهلة الطلب - يرجى المحاولة مرة أخرى",authRequired:"مطلوب مصادقة - يرجى تسجيل الدخول",accessDenied:"تم رفض الوصول - صلاحيات غير كافية",notFound:"المورد غير موجود",serverError:"خطأ في الخادم - يرجى المحاولة لاحقاً",invalidData:"بيانات غير صالحة مقدمة",connectionFailed:"فشل الاتصال",loadingFailed:"فشل في تحميل البيانات",saveFailed:"فشل في حفظ التغييرات",deleteFailed:"فشل في حذف العنصر",updateFailed:"فشل في تحديث العنصر",exportFailed:"فشل في تصدير البيانات",importFailed:"فشل في استيراد البيانات",validationError:"خطأ في التحقق",unknownError:"حدث خطأ غير معروف"},loading:{general:"جاري التحميل...",robots:"جاري تحميل الروبوتات...",sessions:"جاري تحميل الجلسات...",alerts:"جاري تحميل التنبيهات...",dashboard:"جاري تحميل لوحة التحكم...",saving:"جاري الحفظ...",deleting:"جاري الحذف...",updating:"جاري التحديث...",exporting:"جاري التصدير...",importing:"جاري الاستيراد...",connecting:"جاري الاتصال..."}},es:{login:{title:"SteriBOT",subtitle:"ESTERILIZADOR",welcome:"\xa1Bienvenido de nuevo! Por favor, inicia sesi\xf3n en tu cuenta.",email:"Correo electr\xf3nico",emailPlaceholder:"Ingresa tu direcci\xf3n de correo electr\xf3nico",password:"Contrase\xf1a",passwordPlaceholder:"Ingresa tu contrase\xf1a",connect:"Conectar",connecting:"Conectando...",forgotPassword:"\xbfOlvidaste tu contrase\xf1a?",emailRequired:"El correo electr\xf3nico es requerido",emailInvalid:"Por favor ingresa una direcci\xf3n de correo electr\xf3nico v\xe1lida",passwordRequired:"La contrase\xf1a es requerida",passwordMinLength:"La contrase\xf1a debe tener al menos 6 caracteres"},network:{title:"Conectar con",subtitle:"Por favor con\xe9ctate a una red WiFi o habilita tus datos m\xf3viles (4G/5G)",wifi:"Conectar con WiFi",wifiDescription:"Usar conexi\xf3n de red inal\xe1mbrica",mobile:"Conectar con datos m\xf3viles",mobileDescription:"Usar red celular (4G/5G)",connect:"Conectar",connecting:"Conectando..."},connecting:{title:"SteriBOT",subtitle:"Conectando robot",steps:{initializing:"Inicializando conexi\xf3n...",scanning:"Escaneando dispositivos...",establishing:"Estableciendo enlace seguro...",synchronizing:"Sincronizando datos...",established:"\xa1Conexi\xf3n establecida!"},complete:"% Completado",lookingFor:"Buscando tu dispositivo inteligente",ensureDevice:"Por favor aseg\xfarate de que tu dispositivo SteriBOT est\xe9 encendido y dentro del alcance",wifi:"WiFi",mobileData:"Datos m\xf3viles"},robot:{title:"SteriBOT",subtitle:"Robot de desinfecci\xf3n aut\xf3nomo",connected:"Conectado",active:"Activo",startScan:"Iniciar escaneo"},navigation:{home:"Inicio",dashboard:"Panel de control",robotDetails:"Detalles del robot",robotsList:"Lista de robots",sterilizationHistory:"Historial de esterilizaci\xf3n",obstacleDetection:"Detecci\xf3n de obst\xe1culos",profile:"Perfil",logout:"Cerrar sesi\xf3n"},home:{title:"Centro de control SteriBOT",subtitle:"Sistema de gesti\xf3n de robots",configurationScanning:"Configuraci\xf3n / Escaneo del espacio",robotStatus:"Estado del robot",parameters:"Par\xe1metros",mapView:"Vista del mapa",cameraView:"Vista de c\xe1mara",directionControls:"Controles de direcci\xf3n",reset:"Reiniciar",startSterilization:"Iniciar esterilizaci\xf3n",spaceConfiguration:"Configuraci\xf3n del espacio",sterilizationConfig:"Configuraci\xf3n de esterilizaci\xf3n",position:"Posici\xf3n",battery:"Bater\xeda",speed:"Velocidad",resolution:"Resoluci\xf3n",scanRange:"Rango de escaneo",updateRate:"Tasa de actualizaci\xf3n",stopScan:"Detener escaneo",saveMap:"Guardar mapa",mapSaved:"\xa1Mapa guardado!",resetMap:"Reiniciar mapa"},dashboard:{title:"Panel de control",overview:"Resumen del sistema",statistics:"Estad\xedsticas",activeConnections:"Conexiones activas",totalUsers:"Usuarios totales",sterilizedAreas:"\xc1reas esterilizadas",efficiency:"Eficiencia",bacteriaDetection:"Detecci\xf3n de bacterias",recentSessions:"Sesiones recientes",sessionHistory:"Historial de sesiones",date:"Fecha",duration:"Duraci\xf3n",area:"\xc1rea",status:"Estado",completed:"Completado",inProgress:"En progreso",failed:"Fallido",todaysConnections:"Conexiones de hoy",todaysUsers:"Usuarios de hoy",sterilizedZones:"Zonas esterilizadas",fromSterilizedZones:"de 100 zonas esterilizadas",operatingRoomA:"Quir\xf3fano A",surgeryRoom:"Sala de cirug\xeda",mainCorridor:"Pasillo principal",operatingRoomB:"Quir\xf3fano B",log:"Registro",others:"Otros",bacteriaTypes:{eColi:"E. coli",staphylococcus:"Estafilococo",pseudomonas:"Pseudomonas",others:"Otros"},bacteriaDetected24h:"Bacterias detectadas (24h)",total:"Total",zone:"Zona",sterilizationLogChart:"Gr\xe1fico de registros de esterilizaci\xf3n",dateTime:"Fecha/Hora",logStatus:"Estado del registro",firstLog:"1er Registro",secondLog:"2do Registro",thirdLog:"3er Registro",fourthLog:"4to Registro",fifthLog:"5to Registro",sixthLog:"6to Registro",activeRobots:"Robots Activos",totalRobots:"Total Robots",criticalAlerts:"Alertas Cr\xedticas"},robotDetails:{title:"Detalles del robot",information:"Informaci\xf3n del robot",model:"Modelo",serialNumber:"N\xfamero de serie",status:"Estado",batteryLevel:"Nivel de bater\xeda",lastMaintenance:"\xdaltimo mantenimiento",operatingHours:"Horas de operaci\xf3n",specifications:"Especificaciones",dimensions:"Dimensiones",weight:"Peso",maxSpeed:"Velocidad m\xe1xima",uvLamps:"L\xe1mparas UV",coverage:"\xc1rea de cobertura",maintenance:"Mantenimiento",schedule:"Programaci\xf3n",nextService:"Pr\xf3ximo servicio",serviceHistory:"Historial de servicio",weekDays:{mon:"Lun",tue:"Mar",wed:"Mi\xe9",thu:"Jue",fri:"Vie",sat:"S\xe1b",sun:"Dom"},months:{january:"Enero",february:"Febrero",march:"Marzo",april:"Abril",may:"Mayo",june:"Junio",july:"Julio",august:"Agosto",september:"Septiembre",october:"Octubre",november:"Noviembre",december:"Diciembre"},scheduledTasks:"Tareas programadas",operatingRoomSterilization:"Esterilizaci\xf3n quir\xf3fano A",robotMaintenanceCheck:"Verificaci\xf3n mantenimiento robot",preSurgeryProtocol:"Protocolo de esterilizaci\xf3n pre-cirug\xeda",weeklyMaintenance:"Rutina de mantenimiento semanal",scheduled:"Programado",completed:"Completado",inProgress:"En progreso",high:"Alto",medium:"Medio",low:"Bajo",sterilization:"Esterilizaci\xf3n",maintenanceType:"Mantenimiento",operatingRoomA:"Quir\xf3fano A",robotStation:"Estaci\xf3n del robot",drSmith:"Dr. Smith",techTeam:"Equipo t\xe9cnico",mainCorridorCleaning:"Limpieza pasillo principal",emergencyRoomSterilization:"Esterilizaci\xf3n sala de emergencias",dailyCorridorSterilization:"Esterilizaci\xf3n diaria del pasillo",postIncidentSterilization:"Esterilizaci\xf3n post-incidente",mainCorridor:"Pasillo principal",emergencyRoom:"Sala de emergencias",nightShift:"Turno nocturno",drJohnson:"Dr. Johnson",showCalendar:"Mostrar calendario",hideCalendar:"Ocultar calendario",edit:"Editar",day:"D\xeda",week:"Semana",month:"Mes",year:"A\xf1o",addTask:"Agregar tarea",taskTitle:"T\xedtulo de la tarea",date:"Fecha",time:"Hora",duration:"Duraci\xf3n (minutos)",priority:"Prioridad",roomLocation:"Sala/Ubicaci\xf3n",assignedTo:"Asignado a",notes:"Notas",create:"Crear",cancel:"Cancelar",editTask:"Editar tarea",deleteTask:"Eliminar tarea",save:"Guardar",minutes:"minutos"},robotsList:{title:"Lista de robots",addRobot:"Agregar robot",addSpace:"Agregar espacio",robotDescription:"Robot de desinfecci\xf3n aut\xf3nomo",startSterilization:"Iniciar esterilizaci\xf3n",name:"Nombre",location:"Ubicaci\xf3n",description:"Descripci\xf3n",roomType:"Tipo de habitaci\xf3n",area:"\xc1rea",addRobotTitle:"Agregar nuevo robot",addSpaceTitle:"Agregar nuevo espacio",cancel:"Cancelar",add:"Agregar",addNewRobot:"Agregar nuevo robot",robotName:"Nombre del robot",enterRobotName:"Ingrese el nombre del robot",selectModel:"Seleccionar modelo",serialNumber:"N\xfamero de serie",enterSerialNumber:"Ingrese el n\xfamero de serie",enterLocation:"Ingrese la ubicaci\xf3n",enterDescription:"Ingrese la descripci\xf3n",addNewSpace:"Agregar nuevo espacio",spaceName:"Nombre del espacio",enterSpaceName:"Ingrese el nombre del espacio",selectRoomType:"Seleccionar tipo de habitaci\xf3n",enterArea:"Ingrese el \xe1rea",operatingRoom:"Quir\xf3fano",surgeryRoom:"Sala de cirug\xeda",corridor:"Pasillo",laboratory:"Laboratorio"},profile:{title:"Perfil",personalInfo:"Informaci\xf3n personal",name:"Nombre",email:"Correo electr\xf3nico",role:"Rol",department:"Departamento",phone:"Tel\xe9fono",settings:"Configuraciones",notifications:"Notificaciones",language:"Idioma",timezone:"Zona horaria",theme:"Tema",security:"Seguridad",changePassword:"Cambiar contrase\xf1a",twoFactor:"Autenticaci\xf3n de dos factores",loginHistory:"Historial de inicio de sesi\xf3n",editProfile:"Editar perfil",accountStatus:"Estado de la cuenta",memberSince:"Miembro desde",lastLogin:"\xdaltimo inicio de sesi\xf3n",accessLevel:"Nivel de acceso",administrator:"Administrador",details:"Detalles",lastName:"Apellido",jobTitle:"T\xedtulo del trabajo",doctor:"Doctor",cardiology:"Cardiolog\xeda",securitySettings:"Acciones r\xe1pidas",downloadData:"Descargar informe de actividad",notifications2FA:"Configuraci\xf3n de notificaciones",logoutAllDevices:"Cerrar sesi\xf3n",today:"Hoy",january2024:"Enero 2024"},sterilizationHistory:{title:"Historial de esterilizaci\xf3n",history:"Historial",filter:"Filtrar",dateRange:"Rango de fechas",robotFilter:"Filtro de robot",statusFilter:"Filtro de estado",export:"Exportar",details:"Detalles",startTime:"Hora de inicio",endTime:"Hora de finalizaci\xf3n",coverage:"Cobertura",effectiveness:"Efectividad",robotDescription:"Robot de desinfecci\xf3n aut\xf3nomo",activityHistory:"Historial de actividad",bacteria:"Bacteria",staphAureus:"Estafilococo aureus",log:"Registro",log4:"Registro 4",operationsHistory:"Historial de operaciones",id:"ID",zone:"Zona",duration:"Duraci\xf3n",status:"Estado",efficiency:"Eficiencia",operationDetails:"Detalles de la operaci\xf3n",operationId:"ID de operaci\xf3n",resolve:"Resolver",close:"Cerrar",all:"Todos",completed:"Completado",inProgress:"En progreso",failed:"Incidente",exportData:"Exportar datos",refresh:"Actualizar"},obstacleDetection:{title:"Detecci\xf3n de obst\xe1culos",detection:"Sistema de detecci\xf3n",sensors:"Sensores",alerts:"Alertas",sensitivity:"Sensibilidad",calibration:"Calibraci\xf3n",testMode:"Modo de prueba",sensorStatus:"Estado del sensor",frontSensor:"Sensor frontal",rearSensor:"Sensor trasero",sideSensors:"Sensores laterales",operational:"Operacional",warning:"Advertencia",error:"Error",allTypes:"Todos los tipos",connection:"Conexi\xf3n",battery:"Bater\xeda",human:"Humano",allSeverities:"Todas las severidades",high:"Alto",medium:"Medio",low:"Bajo",severity:"Severidad",type:"Tipo",time:"Hora",location:"Ubicaci\xf3n",action:"Acci\xf3n",resolve:"Resolver",alertDetails:"Detalles de la alerta",alertId:"ID de alerta",description:"Descripci\xf3n",resolveAlert:"Resolver alerta",resolutionNote:"Nota de resoluci\xf3n",enterNote:"Ingrese nota de resoluci\xf3n",markResolved:"Marcar como resuelto",cancel:"Cancelar",close:"Cerrar",robotStopped:"Robot detenido debido a obst\xe1culo",lowBattery:"Advertencia de bater\xeda baja",humanDetected:"Humano detectado en zona de esterilizaci\xf3n",connectionLost:"Conexi\xf3n perdida con el robot",resolved:"Resuelto",pending:"Pendiente",critical:"Cr\xedtico",info:"Informaci\xf3n"},modals:{spaceConfig:{title:"Configuraci\xf3n del espacio",roomType:"Tipo de habitaci\xf3n",dimensions:"Dimensiones",obstacles:"Obst\xe1culos",specialRequirements:"Requisitos especiales",save:"Guardar configuraci\xf3n",cancel:"Cancelar"},sterilizationConfig:{title:"Configuraci\xf3n de esterilizaci\xf3n",mode:"Modo de esterilizaci\xf3n",intensity:"Intensidad UV",duration:"Duraci\xf3n",schedule:"Programaci\xf3n",start:"Iniciar esterilizaci\xf3n",cancel:"Cancelar"}},status:{online:"En l\xednea",offline:"Fuera de l\xednea",charging:"Cargando",working:"Trabajando",idle:"Inactivo",maintenance:"Mantenimiento",error:"Error",connected:"Conectado",disconnected:"Desconectado",active:"Activo",inactive:"Inactivo",resolved:"Resuelto"},common:{cancel:"Cancelar",confirm:"Confirmar",close:"Cerrar",save:"Guardar",edit:"Editar",delete:"Eliminar",add:"Agregar",remove:"Quitar",loading:"Cargando...",error:"Error",success:"\xc9xito",warning:"Advertencia",info:"Informaci\xf3n",yes:"S\xed",no:"No",ok:"OK",back:"Atr\xe1s",next:"Siguiente",previous:"Anterior",finish:"Finalizar",retry:"Reintentar",refresh:"Actualizar"},errors:{networkError:"Error de red - por favor verifica tu conexi\xf3n",timeout:"Tiempo de espera agotado - por favor intenta de nuevo",authRequired:"Autenticaci\xf3n requerida - por favor inicia sesi\xf3n",accessDenied:"Acceso denegado - permisos insuficientes",notFound:"Recurso no encontrado",serverError:"Error del servidor - por favor intenta m\xe1s tarde",invalidData:"Datos inv\xe1lidos proporcionados",connectionFailed:"Fall\xf3 la conexi\xf3n",loadingFailed:"Fall\xf3 la carga de datos",saveFailed:"Fall\xf3 al guardar cambios",deleteFailed:"Fall\xf3 al eliminar elemento",updateFailed:"Fall\xf3 al actualizar elemento",exportFailed:"Fall\xf3 la exportaci\xf3n de datos",importFailed:"Fall\xf3 la importaci\xf3n de datos",validationError:"Error de validaci\xf3n",unknownError:"Ocurri\xf3 un error desconocido"},loading:{general:"Cargando...",robots:"Cargando robots...",sessions:"Cargando sesiones...",alerts:"Cargando alertas...",dashboard:"Cargando panel de control...",saving:"Guardando...",deleting:"Eliminando...",updating:"Actualizando...",exporting:"Exportando...",importing:"Importando...",connecting:"Conectando..."}}},K=e=>X[e]||X.en,$=e=>Q.find(t=>t.code===e)||Q[0],ee=(0,i.createContext)(void 0);function et(e){let{children:t,defaultLanguage:a="en"}=e,[r,n]=(0,i.useState)(a);(0,i.useEffect)(()=>{{let e=localStorage.getItem("steribot-language");e&&["en","fr","ar","es"].includes(e)&&n(e)}},[]),(0,i.useEffect)(()=>{{let e=$(r);document.documentElement.dir=e.rtl?"rtl":"ltr",document.documentElement.lang=r}},[r]);let o=K(r),l=$(r).rtl||!1;return(0,s.jsx)(ee.Provider,{value:{language:r,setLanguage:e=>{n(e);{localStorage.setItem("steribot-language",e);let t=$(e);document.documentElement.dir=t.rtl?"rtl":"ltr",document.documentElement.lang=e}},t:o,isRTL:l},children:t})}function ea(){let e=(0,i.useContext)(ee);if(void 0===e)throw Error("useLanguage must be used within a LanguageProvider");return e}function es(){let{t:e}=ea();return e}let ei=e=>[{title:e.navigation.home,url:"/home",icon:U.A},{title:e.navigation.dashboard,url:"/dashboard",icon:J.A},{title:e.navigation.robotDetails,url:"/robot-details",icon:_.A},{title:e.navigation.robotsList,url:"/robots-list",icon:W.A},{title:e.navigation.sterilizationHistory,url:"/sterilization-history",icon:Z.A},{title:e.navigation.obstacleDetection,url:"/obstacle-detection",icon:G.A},{title:e.navigation.profile,url:"/profile",icon:Y.A}];function er(e){let{activeItem:t,onItemClick:a,onLogout:i,...r}=e,n=es(),o=ei(n);return(0,s.jsxs)(E,{...r,className:"bg-sidebar-gradient border-r-0 text-white",children:[(0,s.jsx)(M,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full overflow-hidden",children:(0,s.jsx)("img",{src:"/images/image.png",alt:"Logo",className:"w-full h-full object-cover"})}),(0,s.jsx)("span",{className:"text-white font-semibold text-lg",children:"SteriBOT"})]})}),(0,s.jsxs)(O,{children:[(0,s.jsx)(I,{children:(0,s.jsx)(P,{children:(0,s.jsx)(H,{children:o.map(e=>(0,s.jsx)(B,{children:(0,s.jsx)(q,{asChild:!0,isActive:t===e.url,className:"text-white data-[active=true]:bg-[#0C6980] hover:bg-[#0C6980]",onClick:()=>null==a?void 0:a(e.url),children:(0,s.jsxs)("button",{className:"w-full flex items-center gap-3 p-3",children:[(0,s.jsx)(e.icon,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:e.title})]})})},e.title))})})}),(0,s.jsx)("div",{className:"mt-auto p-4",children:(0,s.jsxs)("button",{onClick:i,className:"w-full flex items-center gap-3 px-3 py-2 text-white hover:bg-[#0C6980] rounded-lg transition-colors",children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,s.jsx)("path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}),(0,s.jsx)("polyline",{points:"16,17 21,12 16,7"}),(0,s.jsx)("line",{x1:"21",y1:"12",x2:"9",y2:"12"})]}),(0,s.jsx)("span",{children:n.navigation.logout})]})})]}),(0,s.jsx)(L,{})]})}let en=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("div",{ref:t,className:c("rounded-lg border bg-card text-card-foreground shadow-sm",a),...i})});en.displayName="Card";let eo=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("div",{ref:t,className:c("flex flex-col space-y-1.5 p-6",a),...i})});eo.displayName="CardHeader";let el=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("div",{ref:t,className:c("text-2xl font-semibold leading-none tracking-tight",a),...i})});el.displayName="CardTitle",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("div",{ref:t,className:c("text-sm text-muted-foreground",a),...i})}).displayName="CardDescription";let ed=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("div",{ref:t,className:c("p-6 pt-0",a),...i})});ed.displayName="CardContent",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("div",{ref:t,className:c("flex items-center p-6 pt-0",a),...i})}).displayName="CardFooter";let ec=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function em(e){let{className:t,variant:a,...i}=e;return(0,s.jsx)("div",{className:c(ec({variant:a}),t),...i})}var eu=a(3904),ex=a(5525),eh=a(7580),eg=a(6785);function ep(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},[s,r]=(0,i.useState)(null),[n,o]=(0,i.useState)(!1),[l,d]=(0,i.useState)(null),c=es(),{immediate:m=!0,onSuccess:u,onError:x}=a,h=(0,i.useCallback)(async()=>{try{o(!0),d(null);let t=await e();r(t),u&&u(t)}catch(t){let e=ev(t instanceof Error?t.message:"An error occurred",c);d(e),x&&x(e)}finally{o(!1)}},[e,u,x,c]);return(0,i.useEffect)(()=>{m&&h()},[h,m,...t]),{data:s,loading:n,error:l,refetch:h}}function eb(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},[a,s]=(0,i.useState)(!1),[r,n]=(0,i.useState)(null),o=es(),{onSuccess:l,onError:d,onSettled:c}=t;return{mutate:(0,i.useCallback)(async t=>{try{s(!0),n(null);let a=await e(t);return l&&l(a,t),c&&c(a,null,t),a}catch(a){let e=ev(a instanceof Error?a.message:"An error occurred",o);throw n(e),d&&d(e,t),c&&c(null,e,t),a}finally{s(!1)}},[e,l,d,c,o]),loading:a,error:r,reset:()=>n(null)}}function ef(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5e3,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},[s,r]=(0,i.useState)(null),[n,o]=(0,i.useState)(!1),[l,d]=(0,i.useState)(null),c=es(),{enabled:m=!0,onSuccess:u,onError:x}=a,h=(0,i.useCallback)(async()=>{try{if(!m)return;o(!0),d(null);let t=await e();r(t),u&&u(t)}catch(t){let e=ev(t instanceof Error?t.message:"An error occurred",c);d(e),x&&x(e)}finally{o(!1)}},[e,m,u,x,c]);return(0,i.useEffect)(()=>{if(!m)return;h();let e=setInterval(h,t);return()=>clearInterval(e)},[h,t,m]),{data:s,loading:n,error:l,refetch:h}}function ev(e,t){var a,s,i,r,n,o,l,d,c,m,u,x,h;let g={"Network error - please check your connection":(null==t?void 0:null===(a=t.errors)||void 0===a?void 0:a.networkError)||"Network error - please check your connection","Request timeout":(null==t?void 0:null===(s=t.errors)||void 0===s?void 0:s.timeout)||"Request timeout","Authentication required":(null==t?void 0:null===(i=t.errors)||void 0===i?void 0:i.authRequired)||"Authentication required","Access denied":(null==t?void 0:null===(r=t.errors)||void 0===r?void 0:r.accessDenied)||"Access denied","Resource not found":(null==t?void 0:null===(n=t.errors)||void 0===n?void 0:n.notFound)||"Resource not found","Server error":(null==t?void 0:null===(o=t.errors)||void 0===o?void 0:o.serverError)||"Server error","Invalid data":(null==t?void 0:null===(l=t.errors)||void 0===l?void 0:l.invalidData)||"Invalid data"};return e.includes("timeout")?(null==t?void 0:null===(d=t.errors)||void 0===d?void 0:d.timeout)||"Request timeout":e.includes("network")||e.includes("fetch")?(null==t?void 0:null===(c=t.errors)||void 0===c?void 0:c.networkError)||"Network error - please check your connection":e.includes("401")||e.includes("Authentication")?(null==t?void 0:null===(m=t.errors)||void 0===m?void 0:m.authRequired)||"Authentication required":e.includes("403")?(null==t?void 0:null===(u=t.errors)||void 0===u?void 0:u.accessDenied)||"Access denied":e.includes("404")?(null==t?void 0:null===(x=t.errors)||void 0===x?void 0:x.notFound)||"Resource not found":e.includes("500")?(null==t?void 0:null===(h=t.errors)||void 0===h?void 0:h.serverError)||"Server error":g[e]||e}let ej={BASE_URL:a(9509).env.NEXT_PUBLIC_API_URL||"http://192.168.1.147:3001",TIMEOUT:1e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},ey={ROBOTS:{LIST:"/api/robots",DETAIL:e=>"/api/robots/".concat(e),STATUS:e=>"/api/robots/".concat(e,"/status"),CONTROL:e=>"/api/robots/".concat(e,"/control"),MAINTENANCE:e=>"/api/robots/".concat(e,"/maintenance")},OBSTACLES:{ALERTS:"/api/obstacles/alerts",ALERT_DETAIL:e=>"/api/obstacles/alerts/".concat(e),RESOLVE:e=>"/api/obstacles/alerts/".concat(e,"/resolve"),SENSORS:"/api/obstacles/sensors"},DASHBOARD:{OVERVIEW:"/api/dashboard/overview",STATISTICS:"/api/dashboard/statistics",RECENT_ACTIVITY:"/api/dashboard/recent-activity"}},eN={UNAUTHORIZED:401},ew={"Content-Type":"application/json",Accept:"application/json"};class eS{setAuthToken(e){this.authToken=e,e?localStorage.setItem("auth_token",e):localStorage.removeItem("auth_token")}getAuthHeaders(){let e={...ew};return this.authToken&&(e.Authorization="Bearer ".concat(this.authToken)),e}createTimeoutController(){let e=new AbortController;return setTimeout(()=>e.abort(),this.timeout),e}sleep(e){return new Promise(t=>setTimeout(t,e))}handleError(e,t){if(console.error("API Error for ".concat(t,":"),e),"AbortError"===e.name)throw Error("Request timeout");if(e instanceof TypeError&&e.message.includes("fetch"))throw Error("Network error - please check your connection");throw e}async makeRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,s="".concat(this.baseURL).concat(e),i=this.createTimeoutController();try{let e=await fetch(s,{...t,headers:{...this.getAuthHeaders(),...t.headers},signal:i.signal});if(e.status===eN.UNAUTHORIZED)throw this.setAuthToken(null),Error("Authentication required");let a=await e.json();if(!e.ok)throw Error(a.message||"HTTP ".concat(e.status));return a}catch(i){if(a<this.retryAttempts&&this.shouldRetry(i))return await this.sleep(this.retryDelay*a),this.makeRequest(e,t,a+1);this.handleError(i,s)}}shouldRetry(e){var t,a,s,i,r;return!((null===(t=e.message)||void 0===t?void 0:t.includes("Authentication"))||(null===(a=e.message)||void 0===a?void 0:a.includes("400"))||(null===(s=e.message)||void 0===s?void 0:s.includes("401"))||(null===(i=e.message)||void 0===i?void 0:i.includes("403"))||(null===(r=e.message)||void 0===r?void 0:r.includes("404")))}async get(e,t){let a=t?"".concat(e,"?").concat(new URLSearchParams(t)):e;return this.makeRequest(a,{method:"GET"})}async post(e,t){return this.makeRequest(e,{method:"POST",body:t?JSON.stringify(t):void 0})}async put(e,t){return this.makeRequest(e,{method:"PUT",body:t?JSON.stringify(t):void 0})}async patch(e,t){return this.makeRequest(e,{method:"PATCH",body:t?JSON.stringify(t):void 0})}async delete(e){return this.makeRequest(e,{method:"DELETE"})}async upload(e,t,a){let s=new FormData;return s.append("file",t),a&&Object.entries(a).forEach(e=>{let[t,a]=e;s.append(t,String(a))}),this.makeRequest(e,{method:"POST",body:s,headers:{Authorization:this.authToken?"Bearer ".concat(this.authToken):""}})}constructor(){this.authToken=null,this.baseURL=ej.BASE_URL,this.timeout=ej.TIMEOUT,this.retryAttempts=ej.RETRY_ATTEMPTS,this.retryDelay=ej.RETRY_DELAY,this.authToken=localStorage.getItem("auth_token")}}let eC=new eS,eA={get:(e,t)=>eC.get(e,t),post:(e,t)=>eC.post(e,t),put:(e,t)=>eC.put(e,t),patch:(e,t)=>eC.patch(e,t),delete:e=>eC.delete(e)};class eR{async getOverview(){return(await eA.get(ey.DASHBOARD.OVERVIEW)).data}async getStatistics(e){return(await eA.get(ey.DASHBOARD.STATISTICS,e?{period:e}:void 0)).data}async getRecentActivity(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return(await eA.get(ey.DASHBOARD.RECENT_ACTIVITY,{limit:e})).data}async getSystemHealth(){return(await eA.get("".concat(ey.DASHBOARD.OVERVIEW,"/health"))).data}async getPerformanceMetrics(){return(await eA.get("".concat(ey.DASHBOARD.STATISTICS,"/performance"))).data}async getChartData(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"week";return(await eA.get("".concat(ey.DASHBOARD.STATISTICS,"/charts"),{type:e,period:t})).data}async getRobotDistribution(){return(await eA.get("".concat(ey.DASHBOARD.OVERVIEW,"/robots"))).data}async getSessionTrends(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:30;return(await eA.get("".concat(ey.DASHBOARD.STATISTICS,"/trends"),{days:e})).data}async getAlertTrends(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:30;return(await eA.get("".concat(ey.DASHBOARD.STATISTICS,"/alert-trends"),{days:e})).data}async exportDashboardData(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"csv",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"month",a=await fetch("".concat(ey.DASHBOARD.STATISTICS,"/export?format=").concat(e,"&period=").concat(t),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))}});if(!a.ok)throw Error("Failed to export dashboard data");return a.blob()}}let ek=new eR;class eD{async getRobots(e){return(await eA.get(ey.ROBOTS.LIST,e)).data}async getRobot(e){return(await eA.get(ey.ROBOTS.DETAIL(e))).data}async createRobot(e){return(await eA.post(ey.ROBOTS.LIST,e)).data}async updateRobot(e,t){return(await eA.put(ey.ROBOTS.DETAIL(e),t)).data}async deleteRobot(e){await eA.delete(ey.ROBOTS.DETAIL(e))}async getRobotStatus(e){return(await eA.get(ey.ROBOTS.STATUS(e))).data}async controlRobot(e,t){await eA.post(ey.ROBOTS.CONTROL(e),t)}async getMaintenanceHistory(e){return(await eA.get(ey.ROBOTS.MAINTENANCE(e))).data}async scheduleMaintenance(e,t){await eA.post(ey.ROBOTS.MAINTENANCE(e),t)}async getRobotsSummary(){return(await eA.get(ey.ROBOTS.LIST,{summary:!0})).data}async startSterilization(e,t){await eA.post(ey.ROBOTS.CONTROL(e),{action:"start",parameters:t})}async stopSterilization(e){await eA.post(ey.ROBOTS.CONTROL(e),{action:"stop"})}async returnHome(e){await eA.post(ey.ROBOTS.CONTROL(e),{action:"return_home"})}async getRobotRealTimeData(e){return(await eA.get(ey.ROBOTS.STATUS(e))).data}}let eT=new eD;class ez{async getAlerts(e){return(await eA.get(ey.OBSTACLES.ALERTS,e)).data}async getAlert(e){return(await eA.get(ey.OBSTACLES.ALERT_DETAIL(e))).data}async resolveAlert(e,t){return(await eA.post(ey.OBSTACLES.RESOLVE(e),t)).data}async getUnresolvedAlerts(){return(await eA.get(ey.OBSTACLES.ALERTS,{resolved:!1,limit:100})).data.data}async getCriticalAlerts(){return(await eA.get(ey.OBSTACLES.ALERTS,{severity:"critical",resolved:!1,limit:50})).data.data}async getAlertsByRobot(e){return(await eA.get(ey.OBSTACLES.ALERTS,{robotId:e,limit:100})).data.data}async getAlertsSummary(){return(await eA.get(ey.OBSTACLES.ALERTS,{summary:!0})).data}async createAlert(e){return(await eA.post(ey.OBSTACLES.ALERTS,e)).data}async updateAlert(e,t){return(await eA.patch(ey.OBSTACLES.ALERT_DETAIL(e),t)).data}async deleteAlert(e){await eA.delete(ey.OBSTACLES.ALERT_DETAIL(e))}async getSensorStatus(){return(await eA.get(ey.OBSTACLES.SENSORS)).data}async testSensors(){return(await eA.post("".concat(ey.OBSTACLES.SENSORS,"/test"))).data}async calibrateSensors(e){await eA.post("".concat(ey.OBSTACLES.SENSORS,"/calibrate"),{sensorIds:e||[]})}}let eE=new ez;var eL=a(3319);let eF=(e,t)=>t&&t.length>0?t.filter(e=>"session_started"===e.type||"session_completed"===e.type).map(e=>{var t,a;let s=(0,eL.GP)(new Date(e.timestamp),"dd MMM yyyy, HH:mm"),i=e.description.includes("Room")&&(null===(t=e.description.split(" in ")[1])||void 0===t?void 0:t.split(".")[0])||"Unknown";return{date:s,zone:i,duration:"session_completed"===e.type?(null===(a=e.description.match(/(\d+\.\d+) min/))||void 0===a?void 0:a[1])+" min":"In progress",status:"session_completed"===e.type?"Log 5":"Log 4"}}):e&&e.dashboard?[{date:"17 June 2025, 14:40",zone:e.dashboard.operatingRoomA,duration:"13.5 min",status:"".concat(e.dashboard.log," 5")},{date:"17 June 2025, 13:20",zone:e.dashboard.surgeryRoom,duration:"8.2 min",status:"".concat(e.dashboard.log," 5")},{date:"17 June 2025, 12:00",zone:e.dashboard.mainCorridor,duration:"14.8 min",status:"".concat(e.dashboard.log," 4")},{date:"17 June 2025, 10:45",zone:e.dashboard.operatingRoomB,duration:"12.3 min",status:"".concat(e.dashboard.log," 4")}]:[{date:"17 June 2025, 14:40",zone:"Operating Room A",duration:"13.5 min",status:"Log 5"},{date:"17 June 2025, 13:20",zone:"Surgery Room",duration:"8.2 min",status:"Log 5"},{date:"17 June 2025, 12:00",zone:"Main Corridor",duration:"14.8 min",status:"Log 4"},{date:"17 June 2025, 10:45",zone:"Operating Room B",duration:"12.3 min",status:"Log 4"}],eM=(e,t)=>{if(t&&t.length>0){let e=["bg-blue-500","bg-purple-500","bg-green-500","bg-orange-500"];return t.map((t,a)=>({name:t.label||"Unknown",percentage:Math.round(t.value),color:e[a%e.length]}))}return e&&e.dashboard&&e.dashboard.bacteriaTypes?[{name:e.dashboard.bacteriaTypes.eColi,percentage:35,color:"bg-blue-500"},{name:e.dashboard.bacteriaTypes.staphylococcus,percentage:28,color:"bg-purple-500"},{name:e.dashboard.bacteriaTypes.pseudomonas,percentage:22,color:"bg-green-500"},{name:e.dashboard.bacteriaTypes.others,percentage:15,color:"bg-orange-500"}]:[{name:"E. coli",percentage:35,color:"bg-blue-500"},{name:"Staphylococcus",percentage:28,color:"bg-purple-500"},{name:"Pseudomonas",percentage:22,color:"bg-green-500"},{name:"Others",percentage:15,color:"bg-orange-500"}]};function eO(){var e,t,a,i,r,n,o,l,d,c,m,x,h,g,p,b,f,v,j,y,N,S,C;let A=es(),{overview:R,statistics:k,recentActivity:D,systemHealth:T,performanceMetrics:z,refetchAll:E,loading:L,error:F}=function(){var e;let t=ef(()=>ek.getOverview(),1e4,{onError:e=>console.error("Failed to fetch dashboard overview:",e)}),a=ep(()=>ek.getStatistics(void 0),[e],{onError:e=>console.error("Failed to fetch dashboard statistics:",e)}),s=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return ef(()=>ek.getRecentActivity(e),15e3,{onError:e=>console.error("Failed to fetch recent activity:",e)})}(),i=ef(()=>ek.getSystemHealth(),3e4,{onError:e=>console.error("Failed to fetch system health:",e)}),r=ef(()=>ek.getPerformanceMetrics(),2e4,{onError:e=>console.error("Failed to fetch performance metrics:",e)});return{overview:t,statistics:a,recentActivity:s,systemHealth:i,performanceMetrics:r,refetchAll:()=>{t.refetch(),a.refetch(),s.refetch(),i.refetch(),r.refetch()},loading:t.loading||a.loading||s.loading||i.loading||r.loading,error:t.error||a.error||s.error||i.error||r.error}}(),M=ef(()=>eT.getRobotsSummary(),1e4,{onError:e=>console.error("Failed to fetch robots summary:",e)}),O=ef(()=>eE.getCriticalAlerts(),5e3,{onError:e=>console.error("Failed to fetch critical alerts:",e)}),I=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"week";return ep(()=>ek.getChartData(e,t),[e,t],{onError:t=>console.error("Failed to fetch chart data for ".concat(e,":"),t)})}("bacteria","week"),P=eF(A,D.data),H=eM(A,I.data);return(0,s.jsxs)("div",{className:"p-6 space-y-6 bg-teal-50 min-h-screen",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-teal-800",children:(null==A?void 0:null===(e=A.dashboard)||void 0===e?void 0:e.title)||"Dashboard"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[F&&(0,s.jsxs)(u,{variant:"outline",size:"sm",onClick:E,className:"text-red-600 border-red-600 hover:bg-red-50",children:[(0,s.jsx)(G.A,{className:"w-4 h-4 mr-2"}),(null==A?void 0:null===(t=A.errors)||void 0===t?void 0:t.loadingFailed)||"Error"]}),(0,s.jsxs)(u,{variant:"outline",size:"sm",onClick:E,disabled:L,className:"text-teal-600 border-teal-600 hover:bg-teal-50",children:[(0,s.jsx)(eu.A,{className:"w-4 h-4 mr-2 ".concat(L?"animate-spin":"")}),(null==A?void 0:null===(a=A.common)||void 0===a?void 0:a.refresh)||"Refresh"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,s.jsx)(en,{className:"text-white",style:{background:"linear-gradient(to left, #0A3F4C, #0C6980)"},children:(0,s.jsx)(ed,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-teal-100 text-sm",children:(null==A?void 0:null===(i=A.dashboard)||void 0===i?void 0:i.todaysConnections)||"Today's Sessions"}),L?(0,s.jsx)(w,{className:"h-8 w-16 bg-teal-700"}):(0,s.jsx)("p",{className:"text-2xl font-bold",children:null!==(f=null!==(b=null===(r=k.data)||void 0===r?void 0:r.sessionsToday)&&void 0!==b?b:null===(n=R.data)||void 0===n?void 0:n.completedSessions)&&void 0!==f?f:53})]}),(0,s.jsx)(ex.A,{className:"w-8 h-8 text-teal-200"})]})})}),(0,s.jsx)(en,{className:"text-white",style:{background:"linear-gradient(to right, #0A3F4C, #0C6980)"},children:(0,s.jsx)(ed,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-blue-100 text-sm",children:(null==A?void 0:null===(o=A.dashboard)||void 0===o?void 0:o.activeRobots)||"Active Robots"}),L?(0,s.jsx)(w,{className:"h-8 w-16 bg-teal-700"}):(0,s.jsx)("p",{className:"text-2xl font-bold",children:null!==(j=null!==(v=null===(l=M.data)||void 0===l?void 0:l.active)&&void 0!==v?v:null===(d=R.data)||void 0===d?void 0:d.activeRobots)&&void 0!==j?j:4})]}),(0,s.jsx)(eh.A,{className:"w-8 h-8 text-teal-200"})]})})}),(0,s.jsx)(en,{className:"text-white",style:{background:"linear-gradient(to left, #0A3F4C, #0C6980)"},children:(0,s.jsx)(ed,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-teal-100 text-sm",children:(null==A?void 0:null===(c=A.dashboard)||void 0===c?void 0:c.totalRobots)||"Total Robots"}),L?(0,s.jsx)(w,{className:"h-8 w-16 bg-teal-700"}):(0,s.jsx)("p",{className:"text-2xl font-bold",children:null!==(N=null!==(y=null===(m=M.data)||void 0===m?void 0:m.total)&&void 0!==y?y:null===(x=R.data)||void 0===x?void 0:x.totalRobots)&&void 0!==N?N:8})]}),(0,s.jsx)(eg.A,{className:"w-8 h-8 text-teal-200"})]})})}),(0,s.jsx)(en,{className:"text-white",style:{background:"linear-gradient(to right, #0A3F4C, #0C6980)"},children:(0,s.jsx)(ed,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-blue-100 text-sm",children:(null==A?void 0:null===(h=A.dashboard)||void 0===h?void 0:h.criticalAlerts)||"Critical Alerts"}),L?(0,s.jsx)(w,{className:"h-8 w-16 bg-teal-700"}):(0,s.jsx)("p",{className:"text-2xl font-bold",children:null!==(C=null!==(S=null===(g=O.data)||void 0===g?void 0:g.length)&&void 0!==S?S:null===(p=R.data)||void 0===p?void 0:p.criticalAlerts)&&void 0!==C?C:2})]}),(0,s.jsx)(Z.A,{className:"w-8 h-8 text-teal-200"})]})})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(en,{className:"bg-slate-800 text-white",children:[(0,s.jsx)(eo,{children:(0,s.jsxs)(el,{className:"flex items-center gap-2",children:[(0,s.jsx)(eg.A,{className:"w-5 h-5"}),A.dashboard.sterilizedZones,(0,s.jsx)("span",{className:"text-sm text-gray-400 ml-auto",children:A.dashboard.fromSterilizedZones})]})}),(0,s.jsxs)(ed,{className:"p-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsxs)("div",{className:"relative w-32 h-32",children:[(0,s.jsx)("div",{className:"absolute inset-0 rounded-full border-8 border-teal-600"}),(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-3xl font-bold",children:"95%"}),(0,s.jsx)("div",{className:"text-sm text-gray-400",children:"Sterilization efficiency"})]})})]})}),(0,s.jsxs)("div",{className:"flex justify-between text-sm mt-4",children:[(0,s.jsx)("span",{children:"0%"}),(0,s.jsx)("span",{children:"100%"})]})]})]}),(0,s.jsxs)(en,{className:"bg-slate-800 text-white",children:[(0,s.jsx)(eo,{children:(0,s.jsxs)(el,{className:"flex items-center gap-2",children:[(0,s.jsx)(Z.A,{className:"w-5 h-5"}),A.dashboard.bacteriaDetected24h,(0,s.jsx)("span",{className:"text-sm text-gray-400 ml-auto",children:A.dashboard.total})]})}),(0,s.jsxs)(ed,{className:"p-6",children:[(0,s.jsx)("div",{className:"space-y-4",children:H.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e.color)}),(0,s.jsx)("span",{className:"text-sm",children:e.name})]}),(0,s.jsxs)("span",{className:"text-sm font-semibold",children:[e.percentage,"%"]})]},t))}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)("div",{className:"relative w-24 h-24 mx-auto",children:[(0,s.jsx)("div",{className:"absolute inset-0 rounded-full border-4 border-gray-600"}),(0,s.jsx)("div",{className:"absolute inset-0 rounded-full border-4 border-t-blue-500 border-r-purple-500 border-b-green-500 border-l-orange-500"})]})})]})]})]}),(0,s.jsxs)(en,{className:"bg-slate-800 text-white",children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:A.dashboard.log})}),(0,s.jsxs)(ed,{className:"p-6",children:[(0,s.jsx)("div",{className:"h-64 bg-gradient-to-t from-slate-900 to-slate-700 rounded-lg flex items-end justify-center",children:(0,s.jsx)("div",{className:"text-gray-400 text-sm",children:A.dashboard.sterilizationLogChart})}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-2",children:[(0,s.jsx)("span",{children:A.dashboard.firstLog}),(0,s.jsx)("span",{children:A.dashboard.secondLog}),(0,s.jsx)("span",{children:A.dashboard.thirdLog}),(0,s.jsx)("span",{children:A.dashboard.fourthLog}),(0,s.jsx)("span",{children:A.dashboard.fifthLog}),(0,s.jsx)("span",{children:A.dashboard.sixthLog})]})]})]}),(0,s.jsxs)(en,{className:"bg-slate-800 text-white",children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:A.dashboard.sessionHistory})}),(0,s.jsx)(ed,{children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-gray-700",children:[(0,s.jsx)("th",{className:"text-left p-3 text-gray-400 font-medium",children:A.dashboard.dateTime}),(0,s.jsx)("th",{className:"text-left p-3 text-gray-400 font-medium",children:A.dashboard.zone}),(0,s.jsx)("th",{className:"text-left p-3 text-gray-400 font-medium",children:A.dashboard.duration}),(0,s.jsx)("th",{className:"text-left p-3 text-gray-400 font-medium",children:A.dashboard.logStatus})]})}),(0,s.jsx)("tbody",{children:P.map((e,t)=>(0,s.jsxs)("tr",{className:"border-b border-gray-700 hover:bg-slate-700",children:[(0,s.jsx)("td",{className:"p-3 text-sm",children:e.date}),(0,s.jsx)("td",{className:"p-3 text-sm",children:e.zone}),(0,s.jsx)("td",{className:"p-3 text-sm",children:e.duration}),(0,s.jsx)("td",{className:"p-3",children:(0,s.jsx)(em,{variant:"secondary",className:"bg-teal-600 text-white",children:e.status})})]},t))})]})})})]})]})}var eI=a(5977);let eP=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(eI.bL,{ref:t,className:c("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),...i})});eP.displayName=eI.bL.displayName;let eH=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(eI._V,{ref:t,className:c("aspect-square h-full w-full",a),...i})});eH.displayName=eI._V.displayName;let eB=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(eI.H4,{ref:t,className:c("flex h-full w-full items-center justify-center rounded-full bg-muted",a),...i})});eB.displayName=eI.H4.displayName;var eV=a(3717),eq=a(1788),eU=a(3861),eJ=a(4835);function e_(){var e,t,a,i,r,n,o,l,d,c,m,x,h,g,p,b,f,v,j,y,N,w,S,C,A,R,k;let D=es();return(0,s.jsxs)("div",{className:"p-6 space-y-6 bg-teal-50 min-h-screen",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-teal-800",children:(null==D?void 0:null===(e=D.profile)||void 0===e?void 0:e.title)||"Profile"}),(0,s.jsxs)(u,{variant:"outline",className:"text-teal-600 border-teal-600 hover:bg-teal-50 bg-transparent",children:[(0,s.jsx)(eV.A,{className:"w-4 h-4 mr-2"}),(null==D?void 0:null===(t=D.profile)||void 0===t?void 0:t.editProfile)||"Edit Profile"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(en,{className:" text-white",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:(0,s.jsx)(ed,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,s.jsxs)(eP,{className:"w-24 h-24 mb-4",children:[(0,s.jsx)(eH,{src:"/placeholder-user.jpg",alt:"Mohamed Ali"}),(0,s.jsx)(eB,{className:"bg-teal-600 text-white text-xl",children:"MA"})]}),(0,s.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Mohamed Ali"})]})})}),(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:(null==D?void 0:null===(a=D.profile)||void 0===a?void 0:a.accountStatus)||"Account Status"})}),(0,s.jsxs)(ed,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-600",children:(null==D?void 0:null===(i=D.profile)||void 0===i?void 0:i.accountStatus)||"Account Status"}),(0,s.jsx)(em,{className:"bg-green-100 text-green-800 hover:bg-green-100",children:(null==D?void 0:null===(r=D.status)||void 0===r?void 0:r.active)||"Active"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-600",children:(null==D?void 0:null===(n=D.profile)||void 0===n?void 0:n.memberSince)||"Member Since"}),(0,s.jsx)("span",{className:"font-medium",children:(null==D?void 0:null===(o=D.profile)||void 0===o?void 0:o.january2024)||"January 2024"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-600",children:(null==D?void 0:null===(l=D.profile)||void 0===l?void 0:l.lastLogin)||"Last Login"}),(0,s.jsxs)("span",{className:"font-medium",children:[(null==D?void 0:null===(d=D.profile)||void 0===d?void 0:d.today)||"Today",", 09:15 AM"]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-600",children:(null==D?void 0:null===(c=D.profile)||void 0===c?void 0:c.accessLevel)||"Access Level"}),(0,s.jsx)(em,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:(null==D?void 0:null===(m=D.profile)||void 0===m?void 0:m.administrator)||"Administrator"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:(null==D?void 0:null===(x=D.profile)||void 0===x?void 0:x.details)||"Details"})}),(0,s.jsxs)(ed,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm text-gray-600",children:(null==D?void 0:null===(h=D.profile)||void 0===h?void 0:h.name)||"Name"}),(0,s.jsx)("p",{className:"font-medium",children:"Mohamed"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm text-gray-600",children:(null==D?void 0:null===(g=D.profile)||void 0===g?void 0:g.lastName)||"Last Name"}),(0,s.jsx)("p",{className:"font-medium",children:"Ali"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm text-gray-600",children:(null==D?void 0:null===(p=D.profile)||void 0===p?void 0:p.email)||"Email"}),(0,s.jsx)("p",{className:"font-medium",children:"<EMAIL>"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm text-gray-600",children:(null==D?void 0:null===(b=D.profile)||void 0===b?void 0:b.jobTitle)||"Job Title"}),(0,s.jsx)("p",{className:"font-medium",children:(null==D?void 0:null===(f=D.profile)||void 0===f?void 0:f.doctor)||"Doctor"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm text-gray-600",children:(null==D?void 0:null===(v=D.profile)||void 0===v?void 0:v.department)||"Department"}),(0,s.jsx)("p",{className:"font-medium",children:(null==D?void 0:null===(j=D.profile)||void 0===j?void 0:j.cardiology)||"Cardiology"})]})]})]})]}),(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:(null==D?void 0:null===(y=D.profile)||void 0===y?void 0:y.security)||"Security"})}),(0,s.jsxs)(ed,{className:"space-y-4",children:[(0,s.jsx)("div",{className:"flex justify-between items-center",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:(null==D?void 0:null===(N=D.profile)||void 0===N?void 0:N.email)||"Email Address"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"<EMAIL>"})]})}),(0,s.jsx)("div",{className:"flex justify-between items-center",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:(null==D?void 0:null===(w=D.profile)||void 0===w?void 0:w.changePassword)||"Current Password"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"••••••••••••"})]})})]})]}),(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:(null==D?void 0:null===(S=D.profile)||void 0===S?void 0:S.securitySettings)||"Quick Actions"})}),(0,s.jsxs)(ed,{className:"space-y-3",children:[(0,s.jsxs)(u,{variant:"outline",className:"w-full justify-start bg-transparent",children:[(0,s.jsx)(eq.A,{className:"w-4 h-4 mr-2"}),(null==D?void 0:null===(C=D.profile)||void 0===C?void 0:C.downloadData)||"Download Activity Report"]}),(0,s.jsxs)(u,{variant:"outline",className:"w-full justify-start bg-transparent",children:[(0,s.jsx)(ex.A,{className:"w-4 h-4 mr-2"}),(null==D?void 0:null===(A=D.profile)||void 0===A?void 0:A.twoFactor)||"Two-Factor Authentication"]}),(0,s.jsxs)(u,{variant:"outline",className:"w-full justify-start bg-transparent",children:[(0,s.jsx)(eU.A,{className:"w-4 h-4 mr-2"}),(null==D?void 0:null===(R=D.profile)||void 0===R?void 0:R.notifications2FA)||"Notification Settings"]}),(0,s.jsxs)(u,{variant:"outline",className:"w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 bg-transparent",children:[(0,s.jsx)(eJ.A,{className:"w-4 h-4 mr-2"}),(null==D?void 0:null===(k=D.profile)||void 0===k?void 0:k.logoutAllDevices)||"Sign Out"]})]})]})]})]})]})}var eW=a(5863);let eZ=i.forwardRef((e,t)=>{let{className:a,value:i,...r}=e;return(0,s.jsx)(eW.bL,{ref:t,className:c("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...r,children:(0,s.jsx)(eW.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(i||0),"%)")}})})});eZ.displayName=eW.bL.displayName;let eG=p.bL,eY=p.l9,eQ=p.ZL;p.bm;let eX=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(p.hJ,{ref:t,className:c("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...i})});eX.displayName=p.hJ.displayName;let eK=i.forwardRef((e,t)=>{let{className:a,children:i,...r}=e;return(0,s.jsxs)(eQ,{children:[(0,s.jsx)(eX,{}),(0,s.jsxs)(p.UC,{ref:t,className:c("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...r,children:[i,(0,s.jsxs)(p.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(b.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});eK.displayName=p.UC.displayName;let e$=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:c("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};e$.displayName="DialogHeader";let e0=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(p.hE,{ref:t,className:c("text-lg font-semibold leading-none tracking-tight",a),...i})});e0.displayName=p.hE.displayName,i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(p.VY,{ref:t,className:c("text-sm text-muted-foreground",a),...i})}).displayName=p.VY.displayName;var e1=a(330),e2=a(6474),e4=a(7863),e8=a(5196);let e3=e1.bL;e1.YJ;let e6=e1.WT,e5=i.forwardRef((e,t)=>{let{className:a,children:i,...r}=e;return(0,s.jsxs)(e1.l9,{ref:t,className:c("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...r,children:[i,(0,s.jsx)(e1.In,{asChild:!0,children:(0,s.jsx)(e2.A,{className:"h-4 w-4 opacity-50"})})]})});e5.displayName=e1.l9.displayName;let e9=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(e1.PP,{ref:t,className:c("flex cursor-default items-center justify-center py-1",a),...i,children:(0,s.jsx)(e4.A,{className:"h-4 w-4"})})});e9.displayName=e1.PP.displayName;let e7=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(e1.wn,{ref:t,className:c("flex cursor-default items-center justify-center py-1",a),...i,children:(0,s.jsx)(e2.A,{className:"h-4 w-4"})})});e7.displayName=e1.wn.displayName;let te=i.forwardRef((e,t)=>{let{className:a,children:i,position:r="popper",...n}=e;return(0,s.jsx)(e1.ZL,{children:(0,s.jsxs)(e1.UC,{ref:t,className:c("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:r,...n,children:[(0,s.jsx)(e9,{}),(0,s.jsx)(e1.LM,{className:c("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:i}),(0,s.jsx)(e7,{})]})})});te.displayName=e1.UC.displayName,i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(e1.JU,{ref:t,className:c("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...i})}).displayName=e1.JU.displayName;let tt=i.forwardRef((e,t)=>{let{className:a,children:i,...r}=e;return(0,s.jsxs)(e1.q7,{ref:t,className:c("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...r,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(e1.VF,{children:(0,s.jsx)(e8.A,{className:"h-4 w-4"})})}),(0,s.jsx)(e1.p4,{children:i})]})});tt.displayName=e1.q7.displayName,i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(e1.wv,{ref:t,className:c("-mx-1 my-1 h-px bg-muted",a),...i})}).displayName=e1.wv.displayName;var ta=a(968);let ts=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),ti=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(ta.b,{ref:t,className:c(ts(),a),...i})});ti.displayName=ta.b.displayName;let tr=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)("textarea",{className:c("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...i})});tr.displayName="Textarea";var tn=a(9074),to=a(2355),tl=a(3052),td=a(4616),tc=a(4186),tm=a(4516),tu=a(2525),tx=a(4229),th=a(537),tg=a(6517),tp=a(6140),tb=a(1539),tf=a(381);let tv=Array.from({length:24},(e,t)=>{let a=t.toString().padStart(2,"0");return"".concat(a,":00")}),tj=e=>e&&e.robotDetails&&e.robotDetails.weekDays?[e.robotDetails.weekDays.mon,e.robotDetails.weekDays.tue,e.robotDetails.weekDays.wed,e.robotDetails.weekDays.thu,e.robotDetails.weekDays.fri,e.robotDetails.weekDays.sat,e.robotDetails.weekDays.sun]:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],ty=e=>e&&e.robotDetails&&e.robotDetails.months?[e.robotDetails.months.january,e.robotDetails.months.february,e.robotDetails.months.march,e.robotDetails.months.april,e.robotDetails.months.may,e.robotDetails.months.june,e.robotDetails.months.july,e.robotDetails.months.august,e.robotDetails.months.september,e.robotDetails.months.october,e.robotDetails.months.november,e.robotDetails.months.december]:["January","February","March","April","May","June","July","August","September","October","November","December"],tN=e=>e&&e.robotDetails?[{id:1,title:e.robotDetails.operatingRoomSterilization,date:"2025-06-11",time:"08:30",duration:120,type:e.robotDetails.sterilization,room:e.robotDetails.operatingRoomA,priority:e.robotDetails.high,assignedTo:e.robotDetails.drSmith,notes:e.robotDetails.preSurgeryProtocol,status:e.robotDetails.scheduled},{id:2,title:e.robotDetails.robotMaintenanceCheck,date:"2025-06-11",time:"14:00",duration:60,type:e.robotDetails.maintenanceType,room:e.robotDetails.robotStation,priority:e.robotDetails.medium,assignedTo:e.robotDetails.techTeam,notes:e.robotDetails.weeklyMaintenance,status:e.robotDetails.scheduled},{id:3,title:e.robotDetails.mainCorridorCleaning,date:"2025-06-12",time:"07:00",duration:180,type:e.robotDetails.sterilization,room:e.robotDetails.mainCorridor,priority:e.robotDetails.medium,assignedTo:e.robotDetails.nightShift,notes:e.robotDetails.dailyCorridorSterilization,status:e.robotDetails.completed},{id:4,title:e.robotDetails.emergencyRoomSterilization,date:"2025-06-13",time:"16:00",duration:90,type:e.robotDetails.sterilization,room:e.robotDetails.emergencyRoom,priority:e.robotDetails.high,assignedTo:e.robotDetails.drJohnson,notes:e.robotDetails.postIncidentSterilization,status:e.robotDetails.inProgress}]:[{id:1,title:"Operating Room A Sterilization",date:"2025-06-11",time:"08:30",duration:120,type:"sterilization",room:"Operating Room A",priority:"high",assignedTo:"Dr. Smith",notes:"Pre-surgery sterilization protocol",status:"scheduled"},{id:2,title:"Robot Maintenance Check",date:"2025-06-11",time:"14:00",duration:60,type:"maintenance",room:"Robot Station",priority:"medium",assignedTo:"Tech Team",notes:"Weekly maintenance routine",status:"scheduled"}];function tw(){var e,t,a,r;let n=es(),o=tj(n),l=ty(n),d=tN(n),[c,m]=(0,i.useState)(!1),[h,g]=(0,i.useState)(!1),[p,b]=(0,i.useState)(!1),[f,v]=(0,i.useState)("week"),[j,y]=(0,i.useState)(new Date(2025,5,11)),[N,w]=(0,i.useState)(null),[S,C]=(0,i.useState)({title:"",date:"",time:"09:00",duration:60,type:"sterilization",room:"",priority:"medium",assignedTo:"",notes:""}),A=e=>{let t=new Date(j);switch(f){case"day":t.setDate(t.getDate()+("next"===e?1:-1));break;case"week":t.setDate(t.getDate()+("next"===e?7:-7));break;case"month":t.setMonth(t.getMonth()+("next"===e?1:-1));break;case"year":t.setFullYear(t.getFullYear()+("next"===e?1:-1))}y(t)},R=e=>d.filter(t=>t.date===e),k=(e,t)=>"completed"===t?"bg-green-500":"in-progress"===t?"bg-blue-500":"sterilization"===e?"bg-orange-500":"bg-purple-500",D=e=>{switch(e){case"high":return"text-red-600 bg-red-100";case"medium":return"text-yellow-600 bg-yellow-100";case"low":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}},T=e=>{console.log("Deleting task:",e)},z=()=>(0,s.jsx)("div",{className:"space-y-2",children:tv.map(e=>{let t=d.filter(t=>t.date===j.toISOString().split("T")[0]&&t.time===e);return(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-2 h-16 border-b border-teal-700",children:[(0,s.jsx)("div",{className:"col-span-2 flex items-center text-sm text-teal-200",children:e}),(0,s.jsx)("div",{className:"col-span-10 relative",children:t.map(e=>(0,s.jsxs)("div",{className:"absolute inset-0 rounded p-2 text-xs cursor-pointer ".concat(k(e.type,e.status)),onClick:()=>{w(e),b(!0)},children:[(0,s.jsx)("div",{className:"font-medium text-white",children:e.title}),(0,s.jsx)("div",{className:"text-white opacity-90",children:e.room})]},e.id))})]},e)})}),E=()=>(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-8 gap-4",children:[(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("div",{className:"text-sm text-teal-200 mb-2",children:"Time"})}),o.map((e,t)=>{let a=new Date(j);return a.setDate(a.getDate()-a.getDay()+1+t),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-sm text-teal-100",children:e}),(0,s.jsx)("div",{className:"text-lg font-bold",children:a.getDate()})]},e)})]}),(0,s.jsx)("div",{className:"space-y-2",children:tv.slice(6,20).map((e,t)=>(0,s.jsxs)("div",{className:"grid grid-cols-8 gap-4 h-16",children:[(0,s.jsx)("div",{className:"flex items-center text-sm text-teal-200",children:e}),Array.from({length:7}).map((t,a)=>{let i=new Date(j);i.setDate(i.getDate()-i.getDay()+1+a);let r=i.toISOString().split("T")[0],n=d.filter(t=>t.date===r&&t.time===e);return(0,s.jsx)("div",{className:"relative",children:n.map(e=>(0,s.jsxs)("div",{className:"absolute inset-0 rounded p-1 text-xs cursor-pointer ".concat(k(e.type,e.status)),style:{height:"".concat(Math.min(e.duration/60*64,64),"px")},onClick:()=>{w(e),b(!0)},children:[(0,s.jsx)("div",{className:"font-medium text-white truncate",children:e.title}),(0,s.jsx)("div",{className:"text-white opacity-90 truncate",children:e.room})]},e.id))},a)})]},e))})]}),L=()=>{let e=new Date(j.getFullYear(),j.getMonth(),1),t=new Date(j.getFullYear(),j.getMonth()+1,0).getDate(),a=e.getDay(),i=[];for(let e=0;e<a;e++)i.push(null);for(let e=1;e<=t;e++)i.push(e);return(0,s.jsxs)("div",{className:"grid grid-cols-7 gap-2",children:[o.map(e=>(0,s.jsx)("div",{className:"text-center text-sm text-teal-200 p-2 font-medium",children:e},e)),i.map((e,t)=>{if(!e)return(0,s.jsx)("div",{className:"h-24"},t);let a=R(new Date(j.getFullYear(),j.getMonth(),e).toISOString().split("T")[0]);return(0,s.jsxs)("div",{className:"h-24 border border-teal-700 rounded p-1",children:[(0,s.jsx)("div",{className:"text-sm text-white font-medium mb-1",children:e}),(0,s.jsxs)("div",{className:"space-y-1",children:[a.slice(0,2).map(e=>(0,s.jsx)("div",{className:"text-xs p-1 rounded cursor-pointer ".concat(k(e.type,e.status)),onClick:()=>{w(e),b(!0)},children:(0,s.jsx)("div",{className:"text-white truncate",children:e.title})},e.id)),a.length>2&&(0,s.jsxs)("div",{className:"text-xs text-teal-200",children:["+",a.length-2," more"]})]})]},t)})]})},F=()=>(0,s.jsx)("div",{className:"grid grid-cols-4 gap-4",children:l.map((e,t)=>(0,s.jsx)(en,{className:"bg-teal-700 text-white cursor-pointer hover:bg-teal-600",children:(0,s.jsxs)(ed,{className:"p-4",children:[(0,s.jsx)("h3",{className:"font-medium mb-2",children:e}),(0,s.jsx)("div",{className:"text-2xl font-bold",children:d.filter(e=>{let a=new Date(e.date);return a.getMonth()===t&&a.getFullYear()===j.getFullYear()}).length}),(0,s.jsx)("div",{className:"text-sm text-teal-200",children:"tasks"})]})},e))});return(0,s.jsxs)("div",{className:"p-6 space-y-6 bg-teal-50 min-h-screen",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-teal-800",children:(null==n?void 0:null===(e=n.robotDetails)||void 0===e?void 0:e.title)||"Robot Details"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(u,{variant:"outline",onClick:()=>m(!c),className:"text-teal-600 border-teal-600 hover:bg-teal-50",children:[(0,s.jsx)(tn.A,{className:"w-4 h-4 mr-2"}),c?(null==n?void 0:null===(t=n.robotDetails)||void 0===t?void 0:t.hideCalendar)||"Hide Calendar":(null==n?void 0:null===(a=n.robotDetails)||void 0===a?void 0:a.showCalendar)||"Show Calendar"]}),(0,s.jsxs)(u,{className:"bg-teal-600 hover:bg-teal-700",children:[(0,s.jsx)(eV.A,{className:"w-4 h-4 mr-2"}),(null==n?void 0:null===(r=n.robotDetails)||void 0===r?void 0:r.edit)||"Edit"]})]})]}),c?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(u,{variant:"outline",size:"sm",onClick:()=>A("prev"),children:(0,s.jsx)(to.A,{className:"w-4 h-4"})}),(0,s.jsx)("h2",{className:"text-xl font-semibold min-w-[200px] text-center",children:(()=>{let e=new Date(j),t=new Date(j);switch(f){case"day":return"".concat(e.toLocaleDateString());case"week":return e.setDate(e.getDate()-e.getDay()+1),t.setDate(e.getDate()+6),"".concat(e.toLocaleDateString()," - ").concat(t.toLocaleDateString());case"month":return"".concat(l[e.getMonth()]," ").concat(e.getFullYear());case"year":return"".concat(e.getFullYear());default:return""}})()}),(0,s.jsx)(u,{variant:"outline",size:"sm",onClick:()=>A("next"),children:(0,s.jsx)(tl.A,{className:"w-4 h-4"})})]}),(0,s.jsx)("div",{className:"flex gap-2",children:["day","week","month","year"].map(e=>(0,s.jsx)(u,{variant:"outline",size:"sm",className:f===e?"bg-teal-600 text-white":"",onClick:()=>v(e),children:e.charAt(0).toUpperCase()+e.slice(1)},e))})]}),(0,s.jsxs)(u,{onClick:()=>g(!0),className:" hover:bg-teal-700",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:[(0,s.jsx)(td.A,{className:"w-4 h-4 mr-2"}),"Create new planning"]})]}),(0,s.jsx)(en,{className:" text-white",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:(0,s.jsx)(ed,{className:"p-6",children:(()=>{switch(f){case"day":return z();case"week":default:return E();case"month":return L();case"year":return F()}})()})}),(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:"Scheduled Tasks"})}),(0,s.jsx)(ed,{children:(0,s.jsx)("div",{className:"space-y-3",children:d.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(k(e.type,e.status))}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.title}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 flex items-center gap-4",children:[(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(tn.A,{className:"w-3 h-3"}),e.date]}),(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(tc.A,{className:"w-3 h-3"}),e.time]}),(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(tm.A,{className:"w-3 h-3"}),e.room]}),(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(Y.A,{className:"w-3 h-3"}),e.assignedTo]})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(em,{className:D(e.priority),children:e.priority}),(0,s.jsx)(em,{variant:"outline",children:e.status}),(0,s.jsx)(u,{size:"sm",variant:"outline",onClick:()=>{w(e),b(!0)},children:(0,s.jsx)(eV.A,{className:"w-3 h-3"})}),(0,s.jsx)(u,{size:"sm",variant:"outline",onClick:()=>T(e.id),children:(0,s.jsx)(tu.A,{className:"w-3 h-3"})})]})]},e.id))})})]}),(0,s.jsx)(eG,{open:h,onOpenChange:g,children:(0,s.jsxs)(eK,{className:"max-w-2xl",children:[(0,s.jsx)(e$,{children:(0,s.jsx)(e0,{children:"Create New Task"})}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"title",children:"Task Title"}),(0,s.jsx)(x,{id:"title",value:S.title,onChange:e=>C({...S,title:e.target.value}),placeholder:"Enter task title"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"type",children:"Type"}),(0,s.jsxs)(e3,{value:S.type,onValueChange:e=>C({...S,type:e}),children:[(0,s.jsx)(e5,{children:(0,s.jsx)(e6,{})}),(0,s.jsxs)(te,{children:[(0,s.jsx)(tt,{value:"sterilization",children:"Sterilization"}),(0,s.jsx)(tt,{value:"maintenance",children:"Maintenance"}),(0,s.jsx)(tt,{value:"inspection",children:"Inspection"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"date",children:"Date"}),(0,s.jsx)(x,{id:"date",type:"date",value:S.date,onChange:e=>C({...S,date:e.target.value})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"time",children:"Time"}),(0,s.jsxs)(e3,{value:S.time,onValueChange:e=>C({...S,time:e}),children:[(0,s.jsx)(e5,{children:(0,s.jsx)(e6,{})}),(0,s.jsx)(te,{children:tv.map(e=>(0,s.jsx)(tt,{value:e,children:e},e))})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"duration",children:"Duration (minutes)"}),(0,s.jsx)(x,{id:"duration",type:"number",value:S.duration,onChange:e=>C({...S,duration:Number(e.target.value)})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"priority",children:"Priority"}),(0,s.jsxs)(e3,{value:S.priority,onValueChange:e=>C({...S,priority:e}),children:[(0,s.jsx)(e5,{children:(0,s.jsx)(e6,{})}),(0,s.jsxs)(te,{children:[(0,s.jsx)(tt,{value:"low",children:"Low"}),(0,s.jsx)(tt,{value:"medium",children:"Medium"}),(0,s.jsx)(tt,{value:"high",children:"High"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"room",children:"Room/Location"}),(0,s.jsx)(x,{id:"room",value:S.room,onChange:e=>C({...S,room:e.target.value}),placeholder:"Enter room or location"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"assignedTo",children:"Assigned To"}),(0,s.jsx)(x,{id:"assignedTo",value:S.assignedTo,onChange:e=>C({...S,assignedTo:e.target.value}),placeholder:"Enter assignee name"})]}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)(ti,{htmlFor:"notes",children:"Notes"}),(0,s.jsx)(tr,{id:"notes",value:S.notes,onChange:e=>C({...S,notes:e.target.value}),placeholder:"Enter additional notes"})]})]}),(0,s.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,s.jsx)(u,{variant:"outline",onClick:()=>g(!1),className:"flex-1",children:"Cancel"}),(0,s.jsxs)(u,{onClick:()=>{console.log("Creating task:",S),g(!1),C({title:"",date:"",time:"09:00",duration:60,type:"sterilization",room:"",priority:"medium",assignedTo:"",notes:""})},className:"flex-1 bg-teal-600 hover:bg-teal-700",children:[(0,s.jsx)(tx.A,{className:"w-4 h-4 mr-2"}),"Create Task"]})]})]})}),(0,s.jsx)(eG,{open:p,onOpenChange:b,children:(0,s.jsxs)(eK,{className:"max-w-2xl",children:[(0,s.jsx)(e$,{children:(0,s.jsx)(e0,{children:"Edit Task"})}),N&&(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Task Title"}),(0,s.jsx)(x,{defaultValue:N.title})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Type"}),(0,s.jsxs)(e3,{defaultValue:N.type,children:[(0,s.jsx)(e5,{children:(0,s.jsx)(e6,{})}),(0,s.jsxs)(te,{children:[(0,s.jsx)(tt,{value:"sterilization",children:"Sterilization"}),(0,s.jsx)(tt,{value:"maintenance",children:"Maintenance"}),(0,s.jsx)(tt,{value:"inspection",children:"Inspection"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Date"}),(0,s.jsx)(x,{type:"date",defaultValue:N.date})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Time"}),(0,s.jsx)(x,{type:"time",defaultValue:N.time})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Duration (minutes)"}),(0,s.jsx)(x,{type:"number",defaultValue:N.duration})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Priority"}),(0,s.jsxs)(e3,{defaultValue:N.priority,children:[(0,s.jsx)(e5,{children:(0,s.jsx)(e6,{})}),(0,s.jsxs)(te,{children:[(0,s.jsx)(tt,{value:"low",children:"Low"}),(0,s.jsx)(tt,{value:"medium",children:"Medium"}),(0,s.jsx)(tt,{value:"high",children:"High"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Room/Location"}),(0,s.jsx)(x,{defaultValue:N.room})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Assigned To"}),(0,s.jsx)(x,{defaultValue:N.assignedTo})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Status"}),(0,s.jsxs)(e3,{defaultValue:N.status,children:[(0,s.jsx)(e5,{children:(0,s.jsx)(e6,{})}),(0,s.jsxs)(te,{children:[(0,s.jsx)(tt,{value:"scheduled",children:"Scheduled"}),(0,s.jsx)(tt,{value:"in-progress",children:"In Progress"}),(0,s.jsx)(tt,{value:"completed",children:"Completed"}),(0,s.jsx)(tt,{value:"cancelled",children:"Cancelled"})]})]})]}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)(ti,{children:"Notes"}),(0,s.jsx)(tr,{defaultValue:N.notes})]})]}),(0,s.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,s.jsx)(u,{variant:"outline",onClick:()=>b(!1),className:"flex-1",children:"Cancel"}),(0,s.jsxs)(u,{onClick:()=>{console.log("Editing task:",N),b(!1),w(null)},className:"flex-1  hover:bg-teal-700",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:[(0,s.jsx)(tx.A,{className:"w-4 h-4 mr-2"}),"Save Changes"]})]})]})})]}):(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(en,{className:" text-white",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:(0,s.jsxs)(ed,{className:"p-6 text-center",children:[(0,s.jsx)("div",{className:"w-32 h-32 mx-auto mb-4 bg-teal-700 rounded-lg flex items-center justify-center",children:(0,s.jsx)(_.A,{className:"w-16 h-16 text-teal-200"})}),(0,s.jsx)("h2",{className:"text-xl font-bold mb-2",children:"S4-CDZ120R"}),(0,s.jsx)("p",{className:"text-teal-200 text-sm mb-4",children:"Sterilization Robot Model"}),(0,s.jsxs)("div",{className:"flex items-center justify-center gap-4 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,s.jsx)("span",{children:"Online"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(th.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"85%"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(tg.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Strong"})]})]}),(0,s.jsx)(u,{className:"w-full mt-4 bg-teal-600 hover:bg-teal-500",children:"Start Sterilization"})]})}),(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsxs)(el,{className:"flex items-center gap-2",children:[(0,s.jsx)(Z.A,{className:"w-5 h-5"}),"Sensors & Connectivity"]})}),(0,s.jsx)(ed,{className:"space-y-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[(0,s.jsx)("div",{className:"bg-green-100 p-3 rounded-lg text-center",children:(0,s.jsx)("div",{className:"text-green-600 font-semibold text-sm",children:"LIDAR"})}),(0,s.jsx)("div",{className:"bg-blue-100 p-3 rounded-lg text-center",children:(0,s.jsx)("div",{className:"text-blue-600 font-semibold text-sm",children:"GPS"})}),(0,s.jsx)("div",{className:"bg-purple-100 p-3 rounded-lg text-center",children:(0,s.jsx)("div",{className:"text-purple-600 font-semibold text-sm",children:"Camera"})})]})})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:"Specifications"})}),(0,s.jsx)(ed,{className:"space-y-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"bg-green-100 p-3 rounded-lg",children:[(0,s.jsx)("div",{className:"text-green-600 text-sm",children:"Efficiency"}),(0,s.jsx)("div",{className:"font-bold",children:"4 rooms/h"})]}),(0,s.jsxs)("div",{className:"bg-purple-100 p-3 rounded-lg",children:[(0,s.jsx)("div",{className:"text-purple-600 text-sm",children:"Status"}),(0,s.jsx)("div",{className:"font-bold",children:"Active"})]}),(0,s.jsxs)("div",{className:"bg-blue-100 p-3 rounded-lg",children:[(0,s.jsx)("div",{className:"text-blue-600 text-sm",children:"Coverage"}),(0,s.jsx)("div",{className:"font-bold",children:"99.9%"})]}),(0,s.jsxs)("div",{className:"bg-orange-100 p-3 rounded-lg",children:[(0,s.jsx)("div",{className:"text-orange-600 text-sm",children:"UV-C"}),(0,s.jsx)("div",{className:"font-bold",children:"99.1%"})]})]})})]}),(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:"Activity History"})}),(0,s.jsx)(ed,{className:"space-y-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"bg-teal-100 p-3 rounded-lg",children:[(0,s.jsx)("div",{className:"text-teal-600 text-sm",children:"Last cycle"}),(0,s.jsx)("div",{className:"font-bold",children:"2h ago"})]}),(0,s.jsxs)("div",{className:"bg-blue-100 p-3 rounded-lg",children:[(0,s.jsx)("div",{className:"text-blue-600 text-sm",children:"Total"}),(0,s.jsx)("div",{className:"font-bold",children:"127 cycles"})]})]})})]}),(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsxs)(el,{className:"flex items-center gap-2",children:[(0,s.jsx)(tp.A,{className:"w-5 h-5"}),"Dimensions & Physics"]})}),(0,s.jsxs)(ed,{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Weight"}),(0,s.jsx)("span",{className:"font-medium",children:"45 kg"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Height"}),(0,s.jsx)("span",{className:"font-medium",children:"120 cm"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Length"}),(0,s.jsx)("span",{className:"font-medium",children:"70 cm"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Total weight"}),(0,s.jsx)("span",{className:"font-medium",children:"18 kg"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Volume"}),(0,s.jsx)("span",{className:"font-medium",children:"Pneumatic ABS-free"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsxs)(el,{className:"flex items-center gap-2",children:[(0,s.jsx)(tb.A,{className:"w-5 h-5"}),"Performance & Energy"]})}),(0,s.jsxs)(ed,{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Battery"}),(0,s.jsx)("span",{className:"font-medium",children:"8500 mAh"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Autonomy"}),(0,s.jsx)("span",{className:"font-medium",children:"8h"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Max speed"}),(0,s.jsx)("span",{className:"font-medium",children:"0.5 m/s"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Consumption"}),(0,s.jsx)("span",{className:"font-medium",children:"200 mAh"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Sterilization mode"}),(0,s.jsx)("span",{className:"font-medium",children:"UV + Spray"})]})]})]}),(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:"System Status"})}),(0,s.jsxs)(ed,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,s.jsx)("span",{className:"text-sm",children:"Battery Level"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"85%"})]}),(0,s.jsx)(eZ,{value:85,className:"h-2"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,s.jsx)("span",{className:"text-sm",children:"UV Lamp Health"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"92%"})]}),(0,s.jsx)(eZ,{value:92,className:"h-2"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,s.jsx)("span",{className:"text-sm",children:"System Temperature"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"42\xb0C"})]}),(0,s.jsx)(eZ,{value:70,className:"h-2"})]})]})]}),(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:"Quick Actions"})}),(0,s.jsxs)(ed,{className:"space-y-2",children:[(0,s.jsxs)(u,{variant:"outline",className:"w-full justify-start bg-transparent",children:[(0,s.jsx)(tf.A,{className:"w-4 h-4 mr-2"}),"System Settings"]}),(0,s.jsxs)(u,{variant:"outline",className:"w-full justify-start bg-transparent",children:[(0,s.jsx)(Z.A,{className:"w-4 h-4 mr-2"}),"Run Diagnostics"]}),(0,s.jsxs)(u,{variant:"outline",className:"w-full justify-start bg-transparent",children:[(0,s.jsx)(tn.A,{className:"w-4 h-4 mr-2"}),"Schedule Maintenance"]})]})]})]})]})]})}var tS=a(7434),tC=a(5040),tA=a(9420),tR=a(1284),tk=a(8611),tD=a(5690);let tT=(e,t)=>{if(!e||0===e.length){var a,s,i,r,n,o,l,d;return[{id:"1",name:"S4-CDZ12RR",description:(null==t?void 0:null===(a=t.robotsList)||void 0===a?void 0:a.robotDescription)||"Autonomous disinfection robot",battery:82,speed:.8,status:(null==t?void 0:null===(s=t.status)||void 0===s?void 0:s.active)||"Active",image:"/placeholder.svg?height=200&width=150"},{id:"2",name:"S4-CDZ12RR",description:(null==t?void 0:null===(i=t.robotsList)||void 0===i?void 0:i.robotDescription)||"Autonomous disinfection robot",battery:92,speed:.8,status:(null==t?void 0:null===(r=t.status)||void 0===r?void 0:r.active)||"Active",image:"/placeholder.svg?height=200&width=150"},{id:"3",name:"S4-CDZ12RR",description:(null==t?void 0:null===(n=t.robotsList)||void 0===n?void 0:n.robotDescription)||"Autonomous disinfection robot",battery:76,speed:.8,status:(null==t?void 0:null===(o=t.status)||void 0===o?void 0:o.active)||"Active",image:"/placeholder.svg?height=200&width=150"},{id:"4",name:"S4-CDZ13RR",description:(null==t?void 0:null===(l=t.robotsList)||void 0===l?void 0:l.robotDescription)||"Autonomous disinfection robot",battery:88,speed:1.2,status:(null==t?void 0:null===(d=t.status)||void 0===d?void 0:d.maintenance)||"Maintenance",image:"/placeholder.svg?height=200&width=150"}]}return e.map(e=>{var a;return{id:e.id,name:e.name,description:e.model||(null==t?void 0:null===(a=t.robotsList)||void 0===a?void 0:a.robotDescription)||"Autonomous disinfection robot",battery:e.batteryLevel,speed:e.status.speed||0,status:tz(e.status,t),image:"/placeholder.svg?height=200&width=150"}})},tz=(e,t)=>{var a,s,i,r,n;return e?e.online?e.operational?e.currentTask?(null==t?void 0:null===(r=t.status)||void 0===r?void 0:r.working)||"Working":(null==t?void 0:null===(n=t.status)||void 0===n?void 0:n.idle)||"Idle":(null==t?void 0:null===(i=t.status)||void 0===i?void 0:i.error)||"Error":(null==t?void 0:null===(s=t.status)||void 0===s?void 0:s.offline)||"Offline":(null==t?void 0:null===(a=t.status)||void 0===a?void 0:a.offline)||"Offline"},tE=[{title:"Configure Existant Space",icon:tf.A,description:"Modifier les espaces existants"}],tL=[{title:"User Manual and security information",icon:tS.A,description:"Documentation et s\xe9curit\xe9"},{title:"Tutorials",icon:tC.A,description:"Guides d'utilisation"},{title:"Contact US / Report incident",icon:tA.A,description:"Support et incidents"}];function tF(){var e,t,a,r,n;let o=es(),{data:l,loading:d,error:c,refetch:m}=ep(()=>eT.getRobots(n),[n={limit:20}],{onError:e=>console.error("Failed to fetch robots:",e)}),h=eb(e=>eT.createRobot(e),{onSuccess:e=>{console.log("Robot created successfully:",e)},onError:e=>{console.error("Failed to create robot:",e)}});eb(e=>{let{id:t,control:a}=e;return eT.controlRobot(t,a)},{onSuccess:(e,t)=>{let{id:a,control:s}=t;console.log("Robot ".concat(a," control action ").concat(s.action," executed successfully"))},onError:(e,t)=>{let{id:a,control:s}=t;console.error("Failed to execute ".concat(s.action," on robot ").concat(a,":"),e)}});let g=tT((null==l?void 0:l.data)||[],o),[p,b]=(0,i.useState)(0),[f,v]=(0,i.useState)(!1),[j,y]=(0,i.useState)(!1),[N,S]=(0,i.useState)({name:"",model:"",serialNumber:"",location:"",description:""}),[C,A]=(0,i.useState)({name:"",type:"",area:"",description:""}),R=Math.ceil(g.length/3),k=async()=>{try{await h.mutate({name:N.name,model:N.model,serialNumber:N.serialNumber,status:{online:!1,operational:!1,currentTask:null,speed:0,temperature:20,uvLampStatus:!1,errorCode:null,lastUpdate:new Date().toISOString()},batteryLevel:100,position:{x:0,y:0,z:0},lastMaintenance:new Date().toISOString(),operatingHours:0,specifications:{dimensions:{width:50,height:120,depth:50},weight:25,maxSpeed:1.5,uvLamps:4,coverageArea:100,batteryCapacity:5e3}}),v(!1),S({name:"",model:"",serialNumber:"",location:"",description:""}),m()}catch(e){console.error("Failed to create robot:",e)}};return(0,s.jsxs)("div",{className:"p-6 space-y-6 bg-teal-50 min-h-screen",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-teal-800",children:(null==o?void 0:null===(e=o.robotsList)||void 0===e?void 0:e.title)||"Robots list"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[c&&(0,s.jsxs)(u,{variant:"outline",size:"sm",onClick:m,className:"text-red-600 border-red-600 hover:bg-red-50",children:[(0,s.jsx)(G.A,{className:"w-4 h-4 mr-2"}),(null==o?void 0:null===(t=o.errors)||void 0===t?void 0:t.loadingFailed)||"Error"]}),(0,s.jsxs)(u,{variant:"outline",size:"sm",onClick:m,disabled:d,className:"text-teal-600 border-teal-600 hover:bg-teal-50",children:[(0,s.jsx)(eu.A,{className:"w-4 h-4 mr-2 ".concat(d?"animate-spin":"")}),(null==o?void 0:null===(a=o.common)||void 0===a?void 0:a.refresh)||"Refresh"]}),(0,s.jsxs)(eG,{open:f,onOpenChange:v,children:[(0,s.jsx)(eY,{asChild:!0,children:(0,s.jsxs)(u,{className:"bg-slate-800 hover:bg-teal-700",children:[(0,s.jsx)(td.A,{className:"w-4 h-4 mr-2"}),(null==o?void 0:null===(r=o.robotsList)||void 0===r?void 0:r.addNewRobot)||"Add New Robot"]})}),(0,s.jsxs)(eK,{className:"max-w-md",children:[(0,s.jsx)(e$,{children:(0,s.jsx)(e0,{children:o.robotsList.addRobotTitle})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"robotName",children:o.robotsList.robotName}),(0,s.jsx)(x,{id:"robotName",value:N.name,onChange:e=>S({...N,name:e.target.value}),placeholder:o.robotsList.enterRobotName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"robotModel",children:o.robotDetails.model}),(0,s.jsxs)(e3,{onValueChange:e=>S({...N,model:e}),children:[(0,s.jsx)(e5,{children:(0,s.jsx)(e6,{placeholder:o.robotsList.selectModel})}),(0,s.jsxs)(te,{children:[(0,s.jsx)(tt,{value:"S4-CDZ12RR",children:"S4-CDZ12RR"}),(0,s.jsx)(tt,{value:"S4-CDZ13RR",children:"S4-CDZ13RR"}),(0,s.jsx)(tt,{value:"S4-CDZ14RR",children:"S4-CDZ14RR"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"serialNumber",children:"Serial Number"}),(0,s.jsx)(x,{id:"serialNumber",value:N.serialNumber,onChange:e=>S({...N,serialNumber:e.target.value}),placeholder:"Enter serial number"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"location",children:"Location"}),(0,s.jsx)(x,{id:"location",value:N.location,onChange:e=>S({...N,location:e.target.value}),placeholder:"Enter location"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"description",children:"Description"}),(0,s.jsx)(tr,{id:"description",value:N.description,onChange:e=>S({...N,description:e.target.value}),placeholder:"Enter description"})]}),(0,s.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,s.jsx)(u,{variant:"outline",onClick:()=>v(!1),className:"flex-1",children:"Cancel"}),(0,s.jsx)(u,{onClick:k,className:"flex-1 bg-teal-600 hover:bg-teal-700",children:"Add Robot"})]})]})]})]})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[p+1," of ",R]}),(0,s.jsx)("div",{className:"flex gap-1",children:Array.from({length:R}).map((e,t)=>(0,s.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(t===p?"bg-teal-600":"bg-gray-300")},t))})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(u,{variant:"outline",size:"sm",onClick:()=>{b(e=>(e-1+R)%R)},disabled:0===p,className:"bg-transparent",children:(0,s.jsx)(to.A,{className:"w-4 h-4"})}),(0,s.jsx)(u,{variant:"outline",size:"sm",onClick:()=>{b(e=>(e+1)%R)},disabled:p===R-1,className:"bg-transparent",children:(0,s.jsx)(tl.A,{className:"w-4 h-4"})})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:d?Array.from({length:3}).map((e,t)=>(0,s.jsx)(en,{className:"text-white",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:(0,s.jsx)(ed,{className:"p-6",children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(w,{className:"w-32 h-40 mx-auto bg-teal-700"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(w,{className:"h-6 w-24 mx-auto mb-2 bg-teal-700"}),(0,s.jsx)(w,{className:"h-4 w-32 mx-auto bg-teal-700"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-center gap-4",children:[(0,s.jsx)(w,{className:"h-4 w-12 bg-teal-700"}),(0,s.jsx)(w,{className:"h-4 w-12 bg-teal-700"})]}),(0,s.jsx)(w,{className:"h-8 w-20 mx-auto bg-teal-700"})]})})},"skeleton-".concat(t))):(()=>{let e=3*p;return g.slice(e,e+3)})().map(e=>(0,s.jsx)(en,{className:"text-white",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:(0,s.jsx)(ed,{className:"p-6",children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-32 h-40 mx-auto bg-teal-700 rounded-lg flex items-center justify-center",children:(0,s.jsx)(_.A,{className:"w-16 h-16 text-teal-200"})}),(0,s.jsx)(u,{size:"sm",variant:"ghost",className:"absolute top-2 right-2 text-white hover:bg-teal-700",children:(0,s.jsx)(tR.A,{className:"w-4 h-4"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-bold mb-1",children:e.name}),(0,s.jsx)("p",{className:"text-teal-200 text-sm",children:e.description})]}),(0,s.jsxs)("div",{className:"flex items-center justify-center gap-4 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(th.A,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:[e.battery,"%"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(tk.A,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:[e.speed," m/s"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full ".concat("Active"===e.status?"bg-green-400":"Maintenance"===e.status?"bg-orange-400":"bg-blue-400")}),(0,s.jsx)("span",{children:e.status})]})]}),(0,s.jsxs)(u,{className:"w-full bg-slate-800 hover:bg-teal-500",disabled:"Active"!==e.status,children:[(0,s.jsx)(tD.A,{className:"w-4 h-4 mr-2"}),"Start sterilization"]})]})})},e.id))})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(en,{className:"text-white",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:"Space management"})}),(0,s.jsxs)(ed,{className:"space-y-4",children:[tE.map((e,t)=>(0,s.jsxs)(u,{variant:"secondary",className:"w-full justify-start bg-white text-teal-800 hover:bg-gray-100",children:[(0,s.jsx)(e.icon,{className:"w-4 h-4 mr-2"}),e.title]},t)),(0,s.jsxs)(eG,{open:j,onOpenChange:y,children:[(0,s.jsx)(eY,{asChild:!0,children:(0,s.jsxs)(u,{variant:"outline",className:"w-full justify-start border-white text-white hover:bg-teal-700 bg-transparent",children:[(0,s.jsx)(td.A,{className:"w-4 h-4 mr-2"}),"Add new space"]})}),(0,s.jsxs)(eK,{className:"max-w-md",children:[(0,s.jsx)(e$,{children:(0,s.jsx)(e0,{children:"Add New Space"})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"spaceName",children:"Space Name"}),(0,s.jsx)(x,{id:"spaceName",value:C.name,onChange:e=>A({...C,name:e.target.value}),placeholder:"Enter space name"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"spaceType",children:"Space Type"}),(0,s.jsxs)(e3,{onValueChange:e=>A({...C,type:e}),children:[(0,s.jsx)(e5,{children:(0,s.jsx)(e6,{placeholder:"Select space type"})}),(0,s.jsxs)(te,{children:[(0,s.jsx)(tt,{value:"operating-room",children:"Operating Room"}),(0,s.jsx)(tt,{value:"corridor",children:"Corridor"}),(0,s.jsx)(tt,{value:"patient-room",children:"Patient Room"}),(0,s.jsx)(tt,{value:"laboratory",children:"Laboratory"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"area",children:"Area (m\xb2)"}),(0,s.jsx)(x,{id:"area",value:C.area,onChange:e=>A({...C,area:e.target.value}),placeholder:"Enter area in square meters",type:"number"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"spaceDescription",children:"Description"}),(0,s.jsx)(tr,{id:"spaceDescription",value:C.description,onChange:e=>A({...C,description:e.target.value}),placeholder:"Enter space description"})]}),(0,s.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,s.jsx)(u,{variant:"outline",onClick:()=>y(!1),className:"flex-1",children:"Cancel"}),(0,s.jsx)(u,{onClick:()=>{console.log("Adding new space:",C),y(!1),A({name:"",type:"",area:"",description:""})},className:"flex-1 bg-teal-600 hover:bg-teal-700",children:"Add Space"})]})]})]})]})]})]}),(0,s.jsxs)(en,{className:"text-white",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:"More information"})}),(0,s.jsx)(ed,{className:"space-y-4",children:tL.map((e,t)=>(0,s.jsxs)(u,{variant:"secondary",className:"w-full justify-between bg-white text-teal-800 hover:bg-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(e.icon,{className:"w-4 h-4 mr-2"}),e.title]}),(0,s.jsx)(tl.A,{className:"w-4 h-4"})]},t))})]})]})]})}var tM=a(646),tO=a(7924);let tI=e=>e&&e.sterilizationHistory&&e.dashboard?[{id:1,startTime:"10/06/2025 08:30",zone:e.dashboard.operatingRoomA,duration:"25 min",status:e.sterilizationHistory.completed,bacteria:e.sterilizationHistory.staphAureus,efficiency:99.8},{id:2,startTime:"10/06/2025 10:00",zone:e.dashboard.mainCorridor,duration:"15 min",status:e.sterilizationHistory.completed,bacteria:"E.coli",efficiency:99.5},{id:3,startTime:"11/06/2025 09:15",zone:e.dashboard.surgeryRoom,duration:"30 min",status:e.sterilizationHistory.failed,bacteria:"Listeria monocyt",efficiency:87.2},{id:4,startTime:"11/06/2025 14:20",zone:"Patient Room 205",duration:"20 min",status:e.sterilizationHistory.completed,bacteria:"Pseudomonas",efficiency:99.9},{id:5,startTime:"12/06/2025 07:45",zone:"Laboratory",duration:"35 min",status:e.sterilizationHistory.completed,bacteria:e.sterilizationHistory.staphAureus,efficiency:99.7}]:[{id:1,startTime:"10/06/2025 08:30",zone:"Operating Room A",duration:"25 min",status:"Completed",bacteria:"Staph. aureus",efficiency:99.8},{id:2,startTime:"10/06/2025 10:00",zone:"Main corridor",duration:"15 min",status:"Completed",bacteria:"E.coli",efficiency:99.5},{id:3,startTime:"11/06/2025 09:15",zone:"Bloc B (Surgery)",duration:"30 min",status:"Incident",bacteria:"Listeria monocyt",efficiency:85.2}],tP=[{time:0,reduction:0},{time:5,reduction:2.5},{time:10,reduction:4.2},{time:15,reduction:5.8},{time:20,reduction:6.9},{time:25,reduction:7.5},{time:30,reduction:7.8}];function tH(){var e;let t=es(),a=tI(t),[r,n]=(0,i.useState)(null),[o,l]=(0,i.useState)("all"),[d,c]=(0,i.useState)(""),[m,h]=(0,i.useState)(!1),g=a.filter(e=>{let t="all"===o||e.status.toLowerCase()===o,a=e.zone.toLowerCase().includes(d.toLowerCase())||e.bacteria.toLowerCase().includes(d.toLowerCase());return t&&a}),p=a.filter(e=>"Completed"===e.status).length,b=a.filter(e=>"Incident"===e.status).length,f=Math.round(a.reduce((e,t)=>e+Number.parseInt(t.duration),0)/a.length),v=e=>{var a,i;let r=(null==t?void 0:null===(a=t.sterilizationHistory)||void 0===a?void 0:a.completed)||"Completed",n=(null==t?void 0:null===(i=t.sterilizationHistory)||void 0===i?void 0:i.failed)||"Incident";return e===r||"Completed"===e?(0,s.jsxs)(em,{className:"bg-green-100 text-green-800 hover:bg-green-100",children:[(0,s.jsx)(tM.A,{className:"w-3 h-3 mr-1"}),r]}):(0,s.jsxs)(em,{className:"bg-orange-100 text-orange-800 hover:bg-orange-100",children:[(0,s.jsx)(G.A,{className:"w-3 h-3 mr-1"}),n]})};return(0,s.jsxs)("div",{className:"p-6 space-y-6 bg-teal-50 min-h-screen",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-teal-800",children:(null==t?void 0:null===(e=t.sterilizationHistory)||void 0===e?void 0:e.title)||"Sterilization History"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(em,{className:"bg-blue-100 text-blue-800 hover:bg-blue-100",children:"Participant"}),(0,s.jsx)(em,{className:"bg-green-100 text-green-800 hover:bg-green-100",children:"Connected"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsx)(en,{className:"bg-teal-800 text-white",children:(0,s.jsxs)(ed,{className:"p-6 text-center",children:[(0,s.jsx)("div",{className:"w-24 h-32 mx-auto mb-4 bg-teal-700 rounded-lg flex items-center justify-center",children:(0,s.jsx)(_.A,{className:"w-12 h-12 text-teal-200"})}),(0,s.jsx)("h2",{className:"text-lg font-bold mb-2",children:"S4-CDZ12RR"}),(0,s.jsx)("p",{className:"text-teal-200 text-sm mb-4",children:"Robot de d\xe9sinfection autonome"}),(0,s.jsxs)("div",{className:"flex items-center justify-center gap-4 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,s.jsx)("span",{children:"82%"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(tc.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"53 min/h"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,s.jsx)("span",{children:"Active"})]})]})]})})}),(0,s.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:"Specifications"})}),(0,s.jsx)(ed,{children:(0,s.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"bg-blue-100 p-4 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-blue-600 text-sm mb-1",children:"Total Operations"}),(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-800",children:a.length}),(0,s.jsx)(tS.A,{className:"w-4 h-4 text-blue-600 mx-auto mt-1"})]}),(0,s.jsxs)("div",{className:"bg-green-100 p-4 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-green-600 text-sm mb-1",children:"Complete"}),(0,s.jsx)("div",{className:"text-2xl font-bold text-green-800",children:p}),(0,s.jsx)(tM.A,{className:"w-4 h-4 text-green-600 mx-auto mt-1"})]}),(0,s.jsxs)("div",{className:"bg-orange-100 p-4 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-orange-600 text-sm mb-1",children:"Incidents"}),(0,s.jsx)("div",{className:"text-2xl font-bold text-orange-800",children:b}),(0,s.jsx)(G.A,{className:"w-4 h-4 text-orange-600 mx-auto mt-1"})]}),(0,s.jsxs)("div",{className:"bg-purple-100 p-4 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-purple-600 text-sm mb-1",children:"Cycle Moyenne"}),(0,s.jsxs)("div",{className:"text-2xl font-bold text-purple-800",children:[f," min"]}),(0,s.jsx)(J.A,{className:"w-4 h-4 text-purple-600 mx-auto mt-1"})]})]})})]}),(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:"Activity History"})}),(0,s.jsx)(ed,{children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)(u,{variant:"outline",className:"justify-start bg-teal-100 text-teal-800 hover:bg-teal-200",children:[(0,s.jsx)(Z.A,{className:"w-4 h-4 mr-2"}),"Bacteria",(0,s.jsx)("span",{className:"ml-auto text-sm",children:"Staph. aureus"})]}),(0,s.jsxs)(u,{variant:"outline",className:"justify-start bg-blue-100 text-blue-800 hover:bg-blue-200",children:[(0,s.jsx)(tS.A,{className:"w-4 h-4 mr-2"}),"Log",(0,s.jsx)("span",{className:"ml-auto text-sm",children:"Log 4"})]})]})})]})]})]}),(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(el,{children:"Journal des Op\xe9rations"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(tO.A,{className:"w-4 h-4 text-gray-500"}),(0,s.jsx)(x,{placeholder:"Search operations...",value:d,onChange:e=>c(e.target.value),className:"w-48"})]}),(0,s.jsxs)(e3,{value:o,onValueChange:l,children:[(0,s.jsx)(e5,{className:"w-32",children:(0,s.jsx)(e6,{})}),(0,s.jsxs)(te,{children:[(0,s.jsx)(tt,{value:"all",children:"All"}),(0,s.jsx)(tt,{value:"completed",children:"Completed"}),(0,s.jsx)(tt,{value:"incident",children:"Incident"})]})]}),(0,s.jsxs)(u,{variant:"outline",size:"sm",children:[(0,s.jsx)(eq.A,{className:"w-4 h-4 mr-2"}),"Export"]}),(0,s.jsx)(u,{variant:"outline",size:"sm",children:(0,s.jsx)(eu.A,{className:"w-4 h-4"})})]})]})}),(0,s.jsx)(ed,{children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b",children:[(0,s.jsx)("th",{className:"text-left p-3 font-medium text-gray-600",children:"N\xb0"}),(0,s.jsx)("th",{className:"text-left p-3 font-medium text-gray-600",children:"Start/Time"}),(0,s.jsx)("th",{className:"text-left p-3 font-medium text-gray-600",children:"Zone"}),(0,s.jsx)("th",{className:"text-left p-3 font-medium text-gray-600",children:"Duration"}),(0,s.jsx)("th",{className:"text-left p-3 font-medium text-gray-600",children:"Statut"}),(0,s.jsx)("th",{className:"text-left p-3 font-medium text-gray-600",children:"Bacteria"}),(0,s.jsx)("th",{className:"text-left p-3 font-medium text-gray-600",children:"Efficiency"})]})}),(0,s.jsx)("tbody",{children:g.map(e=>(0,s.jsxs)("tr",{className:"border-b hover:bg-gray-50 cursor-pointer",onClick:()=>{n(e.id),h(!0)},children:[(0,s.jsx)("td",{className:"p-3 font-medium",children:e.id}),(0,s.jsx)("td",{className:"p-3 text-sm",children:e.startTime}),(0,s.jsx)("td",{className:"p-3 text-sm",children:e.zone}),(0,s.jsx)("td",{className:"p-3 text-sm",children:e.duration}),(0,s.jsx)("td",{className:"p-3",children:v(e.status)}),(0,s.jsx)("td",{className:"p-3 text-sm",children:e.bacteria}),(0,s.jsx)("td",{className:"p-3",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(eZ,{value:e.efficiency,className:"w-16 h-2"}),(0,s.jsxs)("span",{className:"text-sm font-medium",children:[e.efficiency,"%"]})]})})]},e.id))})]})})})]}),(0,s.jsxs)(en,{children:[(0,s.jsx)(eo,{children:(0,s.jsx)(el,{children:"Bacteria reduction curve"})}),(0,s.jsx)(ed,{children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,s.jsx)("div",{className:"h-64 relative",children:(0,s.jsx)("div",{className:"absolute inset-0 flex items-end justify-center",children:(0,s.jsxs)("svg",{viewBox:"0 0 400 200",className:"w-full h-full",children:[(0,s.jsx)("defs",{children:(0,s.jsx)("pattern",{id:"grid",width:"40",height:"20",patternUnits:"userSpaceOnUse",children:(0,s.jsx)("path",{d:"M 40 0 L 0 0 0 20",fill:"none",stroke:"#e5e7eb",strokeWidth:"1"})})}),(0,s.jsx)("rect",{width:"100%",height:"100%",fill:"url(#grid)"}),(0,s.jsx)("line",{x1:"40",y1:"180",x2:"380",y2:"180",stroke:"#374151",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"40",y1:"20",x2:"40",y2:"180",stroke:"#374151",strokeWidth:"2"}),(0,s.jsx)("path",{d:"M 40 180 Q 100 160 160 80 Q 220 40 280 30 Q 340 25 380 25",fill:"none",stroke:"#10b981",strokeWidth:"3"}),tP.map((e,t)=>(0,s.jsx)("circle",{cx:40+e.time/30*340,cy:180-e.reduction/8*160,r:"4",fill:"#10b981"},t)),(0,s.jsx)("text",{x:"210",y:"195",textAnchor:"middle",className:"text-xs fill-gray-600",children:"Time (minutes)"}),(0,s.jsx)("text",{x:"25",y:"100",textAnchor:"middle",className:"text-xs fill-gray-600",transform:"rotate(-90 25 100)",children:"Log Reduction"})]})})}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-2",children:[(0,s.jsx)("span",{children:"0"}),(0,s.jsx)("span",{children:"5"}),(0,s.jsx)("span",{children:"10"}),(0,s.jsx)("span",{children:"15"}),(0,s.jsx)("span",{children:"20"}),(0,s.jsx)("span",{children:"25"}),(0,s.jsx)("span",{children:"30"})]})]})})]}),(0,s.jsx)(eG,{open:m,onOpenChange:h,children:(0,s.jsxs)(eK,{className:"max-w-2xl",children:[(0,s.jsx)(e$,{children:(0,s.jsxs)(e0,{children:["Operation Details #",r]})}),r&&(0,s.jsx)("div",{className:"space-y-4",children:(()=>{let e=a.find(e=>e.id===r);return e?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Start Time"}),(0,s.jsx)("p",{className:"font-medium",children:e.startTime})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Duration"}),(0,s.jsx)("p",{className:"font-medium",children:e.duration})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Zone"}),(0,s.jsx)("p",{className:"font-medium",children:e.zone})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Status"}),(0,s.jsx)("div",{className:"mt-1",children:v(e.status)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Bacteria Detected"}),(0,s.jsx)("p",{className:"font-medium",children:e.bacteria})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Efficiency"}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,s.jsx)(eZ,{value:e.efficiency,className:"flex-1"}),(0,s.jsxs)("span",{className:"font-medium",children:[e.efficiency,"%"]})]})]})]}),(0,s.jsxs)("div",{className:"pt-4 border-t",children:[(0,s.jsx)(ti,{children:"Additional Information"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Incident"===e.status?"Sterilization process was interrupted due to sensor malfunction. Manual intervention required.":"Sterilization completed successfully with optimal bacteria reduction achieved."})]})]}):null})()})]})})]})}let tB=e=>e&&e.obstacleDetection?[{id:1,type:e.obstacleDetection.connection,title:"Connection Status",status:"Disconnect",message:"".concat(e.obstacleDetection.connectionLost,". On 2025-06-17 at 22:05."),description:e.obstacleDetection.connectionLost,timestamp:"June 17, 2025 • 10:05 PM",severity:e.obstacleDetection.warning,resolved:!1,icon:tg.A},{id:2,type:e.obstacleDetection.battery,title:"Charging Status",status:e.obstacleDetection.lowBattery,message:"On 2025-06-17 at 22:10.",description:e.obstacleDetection.lowBattery,timestamp:"June 17, 2025 • 10:10 PM",severity:e.obstacleDetection.error,resolved:!1,icon:th.A},{id:3,type:e.obstacleDetection.human,title:"Human Detection",status:"Alert",message:"On 2025-06-17 at 22:15.",description:e.obstacleDetection.humanDetected,timestamp:"June 17, 2025 • 10:15 PM",severity:e.obstacleDetection.critical,resolved:!1,icon:Y.A},{id:4,type:e.obstacleDetection.connection,title:"Connection Restored",status:"Connected",message:"Connection restored on 2025-06-17 at 22:20.",description:"Network connection has been successfully restored. Robot is now online and ready to resume operations.",timestamp:"June 17, 2025 • 10:20 PM",severity:"success",resolved:!0,icon:tg.A}]:[{id:1,type:"connection",title:"Connection Status",status:"Disconnect",message:"Connection issue. On 2025-06-17 at 22:05.",description:"The robot experienced a connection interruption. Communication was lost, causing sterilization to pause until the connection was restored.",timestamp:"June 17, 2025 • 10:05 PM",severity:"warning",resolved:!1,icon:tg.A},{id:2,type:"battery",title:"Charging Status",status:"Low battery",message:"On 2025-06-17 at 22:10.",description:"The robot paused sterilization due to low battery and began charging. Sterilization will resume automatically once charging is complete.",timestamp:"June 17, 2025 • 10:10 PM",severity:"error",resolved:!1,icon:th.A}];function tV(){var e,t,a,r,n,o,l,d,c,m,x,h,g,p,f,v;let j=es(),y=tB(j),[N,w]=(0,i.useState)(null),[S,C]=(0,i.useState)(!1),[A,R]=(0,i.useState)("all"),[k,D]=(0,i.useState)("all"),[T,z]=(0,i.useState)(!1),[E,L]=(0,i.useState)(""),F=y.filter(e=>{let t="all"===A||e.type===A,a="all"===k||e.severity===k;return t&&a}),M=e=>{switch(e){case"critical":return"bg-red-100 border-red-200";case"error":return"bg-pink-100 border-pink-200";case"warning":return"bg-yellow-100 border-yellow-200";case"success":return"bg-green-100 border-green-200";default:return"bg-gray-100 border-gray-200"}},O=(e,t)=>(0,s.jsx)(em,{className:{critical:"bg-red-500 text-white",error:"bg-pink-500 text-white",warning:"bg-orange-500 text-white",success:"bg-green-500 text-white"}[t]||"bg-gray-500 text-white",children:e});return(0,s.jsxs)("div",{className:"p-6 space-y-6 bg-teal-50 min-h-screen",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-teal-800",children:(null==j?void 0:null===(e=j.obstacleDetection)||void 0===e?void 0:e.title)||"Obstacle detection"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(e3,{value:A,onValueChange:R,children:[(0,s.jsx)(e5,{className:"w-32",children:(0,s.jsx)(e6,{placeholder:(null==j?void 0:null===(t=j.obstacleDetection)||void 0===t?void 0:t.type)||"Type"})}),(0,s.jsxs)(te,{children:[(0,s.jsx)(tt,{value:"all",children:(null==j?void 0:null===(a=j.obstacleDetection)||void 0===a?void 0:a.allTypes)||"All Types"}),(0,s.jsx)(tt,{value:"connection",children:(null==j?void 0:null===(r=j.obstacleDetection)||void 0===r?void 0:r.connection)||"Connection"}),(0,s.jsx)(tt,{value:"battery",children:(null==j?void 0:null===(n=j.obstacleDetection)||void 0===n?void 0:n.battery)||"Battery"}),(0,s.jsx)(tt,{value:"human",children:(null==j?void 0:null===(o=j.obstacleDetection)||void 0===o?void 0:o.human)||"Human"})]})]}),(0,s.jsxs)(e3,{value:k,onValueChange:D,children:[(0,s.jsx)(e5,{className:"w-32",children:(0,s.jsx)(e6,{placeholder:(null==j?void 0:null===(l=j.obstacleDetection)||void 0===l?void 0:l.severity)||"Severity"})}),(0,s.jsxs)(te,{children:[(0,s.jsx)(tt,{value:"all",children:(null==j?void 0:null===(d=j.obstacleDetection)||void 0===d?void 0:d.allSeverities)||"All Severity"}),(0,s.jsx)(tt,{value:"critical",children:(null==j?void 0:null===(c=j.obstacleDetection)||void 0===c?void 0:c.critical)||"Critical"}),(0,s.jsx)(tt,{value:"error",children:(null==j?void 0:null===(m=j.obstacleDetection)||void 0===m?void 0:m.error)||"Error"}),(0,s.jsx)(tt,{value:"warning",children:(null==j?void 0:null===(x=j.obstacleDetection)||void 0===x?void 0:x.warning)||"Warning"}),(0,s.jsx)(tt,{value:"success",children:(null==j?void 0:null===(h=j.status)||void 0===h?void 0:h.resolved)||"Success"})]})]}),(0,s.jsxs)(u,{variant:"outline",size:"sm",children:[(0,s.jsx)(eq.A,{className:"w-4 h-4 mr-2"}),(null==j?void 0:null===(g=j.sterilizationHistory)||void 0===g?void 0:g.exportData)||"Export"]}),(0,s.jsx)(u,{variant:"outline",size:"sm",children:(0,s.jsx)(eu.A,{className:"w-4 h-4"})})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsx)(en,{className:"bg-red-50 border-red-200",children:(0,s.jsx)(ed,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-red-600 text-sm",children:[(null==j?void 0:null===(p=j.obstacleDetection)||void 0===p?void 0:p.critical)||"Critical"," ",(null==j?void 0:null===(f=j.obstacleDetection)||void 0===f?void 0:f.alerts)||"Alerts"]}),(0,s.jsx)("p",{className:"text-2xl font-bold text-red-800",children:y.filter(e=>"critical"===e.severity&&!e.resolved).length})]}),(0,s.jsx)(G.A,{className:"w-8 h-8 text-red-500"})]})})}),(0,s.jsx)(en,{className:"bg-orange-50 border-orange-200",children:(0,s.jsx)(ed,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-orange-600 text-sm",children:(null==j?void 0:null===(v=j.obstacleDetection)||void 0===v?void 0:v.warning)||"Warnings"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-orange-800",children:y.filter(e=>"warning"===e.severity&&!e.resolved).length})]}),(0,s.jsx)(eU.A,{className:"w-8 h-8 text-orange-500"})]})})}),(0,s.jsx)(en,{className:"bg-green-50 border-green-200",children:(0,s.jsx)(ed,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-green-600 text-sm",children:"Resolved"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-green-800",children:y.filter(e=>e.resolved).length})]}),(0,s.jsx)(tM.A,{className:"w-8 h-8 text-green-500"})]})})}),(0,s.jsx)(en,{className:"bg-blue-50 border-blue-200",children:(0,s.jsx)(ed,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-blue-600 text-sm",children:"Total Alerts"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-blue-800",children:y.length})]}),(0,s.jsx)(tf.A,{className:"w-8 h-8 text-blue-500"})]})})})]}),(0,s.jsx)("div",{className:"space-y-4",children:F.map(e=>{let t=e.icon;return(0,s.jsx)(en,{className:"".concat(M(e.severity)," cursor-pointer hover:shadow-md transition-shadow ").concat(e.resolved?"opacity-75":""),onClick:()=>{w(e.id),C(!0)},children:(0,s.jsx)(ed,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex items-start gap-4 flex-1",children:[(0,s.jsx)("div",{className:"p-2 bg-white rounded-lg",children:(0,s.jsx)(t,{className:"w-6 h-6 text-teal-600"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-800",children:e.title}),O(e.status,e.severity),e.resolved&&(0,s.jsxs)(em,{className:"bg-green-100 text-green-800",children:[(0,s.jsx)(tM.A,{className:"w-3 h-3 mr-1"}),"Resolved"]})]}),(0,s.jsx)("p",{className:"text-gray-700 mb-2",children:e.message}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:e.description}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[(0,s.jsx)(tc.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.timestamp})]})]})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[!e.resolved&&(0,s.jsx)(u,{size:"sm",variant:"outline",onClick:t=>{t.stopPropagation(),w(e.id),z(!0)},children:"Resolve"}),(0,s.jsx)(u,{size:"sm",variant:"ghost",onClick:t=>{t.stopPropagation(),console.log("Dismiss alert",e.id)},children:(0,s.jsx)(b.A,{className:"w-4 h-4"})})]})]})})},e.id)})}),(0,s.jsx)(eG,{open:S,onOpenChange:C,children:(0,s.jsxs)(eK,{className:"max-w-2xl",children:[(0,s.jsx)(e$,{children:(0,s.jsx)(e0,{children:"Alert Details"})}),N&&(0,s.jsx)("div",{className:"space-y-4",children:(()=>{let e=y.find(e=>e.id===N);if(!e)return null;let t=e.icon;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"p-3 bg-teal-100 rounded-lg",children:(0,s.jsx)(t,{className:"w-8 h-8 text-teal-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:e.title}),(0,s.jsxs)("div",{className:"flex gap-2 mt-1",children:[O(e.status,e.severity),e.resolved&&(0,s.jsx)(em,{className:"bg-green-100 text-green-800",children:"Resolved"})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Type"}),(0,s.jsx)("p",{className:"font-medium capitalize",children:e.type})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Severity"}),(0,s.jsx)("p",{className:"font-medium capitalize",children:e.severity})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Timestamp"}),(0,s.jsx)("p",{className:"font-medium",children:e.timestamp})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Status"}),(0,s.jsx)("p",{className:"font-medium",children:e.resolved?"Resolved":"Active"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{children:"Description"}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:e.description})]}),(0,s.jsxs)("div",{className:"pt-4 border-t",children:[(0,s.jsx)(ti,{children:"Recommended Actions"}),(0,s.jsxs)("ul",{className:"list-disc list-inside text-sm text-gray-600 mt-1 space-y-1",children:["connection"===e.type&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("li",{children:"Check network connectivity"}),(0,s.jsx)("li",{children:"Verify router and access point status"}),(0,s.jsx)("li",{children:"Restart robot if connection persists"})]}),"battery"===e.type&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("li",{children:"Allow robot to complete charging cycle"}),(0,s.jsx)("li",{children:"Check charging station functionality"}),(0,s.jsx)("li",{children:"Monitor battery health indicators"})]}),"human"===e.type&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("li",{children:"Ensure area is clear of personnel"}),(0,s.jsx)("li",{children:"Check safety sensors functionality"}),(0,s.jsx)("li",{children:"Resume operation once area is secure"})]})]})]})]})})()})]})}),(0,s.jsx)(eG,{open:T,onOpenChange:z,children:(0,s.jsxs)(eK,{className:"max-w-md",children:[(0,s.jsx)(e$,{children:(0,s.jsx)(e0,{children:"Resolve Alert"})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{htmlFor:"resolveNote",children:"Resolution Notes"}),(0,s.jsx)(tr,{id:"resolveNote",value:E,onChange:e=>L(e.target.value),placeholder:"Enter details about how this alert was resolved...",className:"mt-1"})]}),(0,s.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,s.jsx)(u,{variant:"outline",onClick:()=>z(!1),className:"flex-1",children:"Cancel"}),(0,s.jsx)(u,{onClick:()=>{console.log("Resolving alert with note:",E),z(!1),L("")},className:"flex-1 bg-teal-600 hover:bg-teal-700",children:"Mark as Resolved"})]})]})]})})]})}let tq={position:{x:12.5,y:8.3},battery:82,speed:.8};function tU(){let e=es();return(0,s.jsxs)("div",{className:"p-4 rounded-lg text-white",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:[(0,s.jsxs)("h2",{className:"flex items-center gap-2 text-white font-bold mb-4",children:[(0,s.jsx)(tk.A,{className:"w-5 h-5"}),e.home.robotStatus]}),(0,s.jsxs)("div",{className:"space-y-3 text-white",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-300",children:e.home.position}),(0,s.jsxs)("span",{children:["X: ",tq.position.x,", Y: ",tq.position.y]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-300",children:e.home.battery}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-24 h-2 bg-[#0A3F4C] rounded-full overflow-hidden border border-[#0C6980]",children:(0,s.jsx)("div",{className:"h-full rounded-full",style:{width:"".concat(tq.battery,"%"),background:"linear-gradient(90deg, #14b8a6, #0C6980)"}})}),(0,s.jsxs)("span",{children:[tq.battery,"%"]})]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-300",children:e.home.speed}),(0,s.jsxs)("span",{children:[tq.speed," m/s"]})]})]})]})}function tJ(){let e=es(),[t,a]=(0,i.useState)({resolution:"",scanRange:5,updateRate:15}),r=(e,t)=>{a(a=>({...a,[e]:t}))};return(0,s.jsxs)("div",{className:"p-4 rounded-lg text-white",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:[(0,s.jsxs)("h2",{className:"flex items-center gap-2 font-bold mb-4",children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"w-5 h-5",children:[(0,s.jsx)("path",{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}),(0,s.jsx)("circle",{cx:"12",cy:"10",r:"3"})]}),e.home.parameters]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm text-gray-200 mb-1",children:e.home.resolution}),(0,s.jsx)("input",{type:"text",value:t.resolution,onChange:e=>r("resolution",e.target.value),className:"w-full p-2 bg-[#0A3F4C] border border-[#0C6980] rounded text-white placeholder-gray-300 focus:border-white focus:outline-none"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm text-gray-200 mb-1",children:[e.home.scanRange,": ",t.scanRange,"m"]}),(0,s.jsx)("input",{type:"range",min:"1",max:"10",value:t.scanRange,onChange:e=>r("scanRange",parseInt(e.target.value)),className:"w-full h-2 bg-[#0A3F4C] rounded-lg appearance-none cursor-pointer range-teal",style:{background:"linear-gradient(to right, #14b8a6 0%, #14b8a6 ".concat((t.scanRange-1)/9*100,"%, #0A3F4C ").concat((t.scanRange-1)/9*100,"%, #0A3F4C 100%)")}}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-300",children:[(0,s.jsx)("span",{children:"1m"}),(0,s.jsx)("span",{children:"10m"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm text-gray-200 mb-1",children:[e.home.updateRate,": ",t.updateRate,"Hz"]}),(0,s.jsx)("input",{type:"range",min:"1",max:"30",value:t.updateRate,onChange:e=>r("updateRate",parseInt(e.target.value)),className:"w-full h-2 bg-[#0A3F4C] rounded-lg appearance-none cursor-pointer range-teal",style:{background:"linear-gradient(to right, #14b8a6 0%, #14b8a6 ".concat((t.updateRate-1)/29*100,"%, #0A3F4C ").concat((t.updateRate-1)/29*100,"%, #0A3F4C 100%)")}}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-300",children:[(0,s.jsx)("span",{children:"1Hz"}),(0,s.jsx)("span",{children:"30Hz"})]})]})]})]})}function t_(e){let{onMapSaved:t,onReset:a,isMapSavedState:r=!1}=e,n=es(),[o,l]=(0,i.useState)(!0),[d,c]=(0,i.useState)(!1);return(0,s.jsxs)("div",{className:"space-y-2 flex flex-col",children:[(0,s.jsx)("button",{onClick:()=>{o?l(!1):(c(!0),null==t||t(),setTimeout(()=>c(!1),2e3))},className:"w-full text-white py-2 px-4 rounded flex items-center justify-center gap-2 mt-auto transition-all hover:scale-105 ".concat(""),style:{background:o?"linear-gradient(90deg, #dc2626, #b91c1c)":"linear-gradient(90deg, #14b8a6, #0C6980)"},children:o?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,s.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,s.jsx)("rect",{x:"9",y:"9",width:"6",height:"6"})]}),n.home.stopScan]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,s.jsx)("path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}),(0,s.jsx)("polyline",{points:"17 21 17 13 7 13 7 21"}),(0,s.jsx)("polyline",{points:"7 3 7 8 15 8"})]}),d?n.home.mapSaved:n.home.saveMap]})}),(0,s.jsxs)("button",{onClick:()=>{l(!0),c(!1),null==a||a()},className:"w-full text-white py-2 px-4 rounded flex items-center justify-center gap-2 transition-all hover:scale-105",style:{background:"linear-gradient(90deg, #14b8a6, #0C6980)"},children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,s.jsx)("path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"}),(0,s.jsx)("path",{d:"M21 3v5h-5"}),(0,s.jsx)("path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"}),(0,s.jsx)("path",{d:"M8 16H3v5"})]}),n.home.resetMap]})]})}function tW(e){let{onMapClick:t,isClickable:a=!1}=e;return(0,s.jsxs)("div",{className:"bg-gray-700 rounded-lg overflow-hidden flex-1 relative ".concat(a?"cursor-pointer hover:bg-gray-600 transition-colors":""),onClick:()=>{a&&t&&t()},children:[(0,s.jsx)("img",{alt:"Map",className:"w-full h-full object-cover"}),a&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 opacity-0 hover:opacity-100 transition-opacity rounded-lg",children:(0,s.jsx)("div",{className:"bg-gray-800 text-white px-4 py-2 rounded-lg text-sm",children:"Click to configure space"})})]})}function tZ(){return(0,s.jsxs)("div",{className:"flex justify-end gap-2 mt-4",children:[(0,s.jsx)("button",{className:"w-10 h-10 p-0 flex items-center justify-center rounded transition-all hover:scale-105",style:{background:"linear-gradient(90deg, #14b8a6, #0C6980)"},children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"16",height:"16",fill:"white",children:(0,s.jsx)("path",{d:"M12 20l-8-8 1.41-1.41L12 17.17l7.59-7.59L20 12l-8 8z"})})}),(0,s.jsx)("button",{className:"w-10 h-10 p-0 flex items-center justify-center rounded transition-all hover:scale-105",style:{background:"linear-gradient(90deg, #14b8a6, #0C6980)"},children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"16",height:"16",fill:"white",children:(0,s.jsx)("path",{d:"M12 4l8 8-1.41 1.41L13 6.41 4.41 14 4 12l8-8z"})})}),(0,s.jsx)("button",{className:"w-10 h-10 p-0 flex items-center justify-center rounded transition-all hover:scale-105",style:{background:"linear-gradient(90deg, #14b8a6, #0C6980)"},children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"16",height:"16",fill:"white",children:(0,s.jsx)("path",{d:"M20 12l-1.41-1.41L13 16.17l-7.59-7.59L4 12l8 8 8-8z"})})}),(0,s.jsx)("button",{className:"w-10 h-10 p-0 flex items-center justify-center rounded transition-all hover:scale-105",style:{background:"linear-gradient(90deg, #14b8a6, #0C6980)"},children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"16",height:"16",fill:"white",children:(0,s.jsx)("path",{d:"M4 12l1.41 1.41L10.83 7.59 12 6.41l1.17 1.17L19 12l-8 8-1.41-1.41L12 15.41 6.41 10 7.59 8.83z"})})})]})}var tG=a(4311),tY=a(1981);function tQ(e){let{isFullScreen:t=!1,onToggleFullScreen:a}=e,[r,n]=(0,i.useState)(!0);return(0,s.jsxs)("div",{className:"bg-gray-700 rounded-lg overflow-hidden flex flex-col ".concat(t?"fixed inset-0 z-50":"flex-1"),children:[(0,s.jsxs)("div",{className:"bg-gray-800 p-3 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(r?"bg-green-500":"bg-red-500")}),(0,s.jsx)("span",{className:"text-sm text-white",children:"Camera Feed"})]}),(0,s.jsx)("button",{onClick:a,className:"text-white hover:text-gray-300 p-1 rounded transition-colors",title:t?"Exit Full Screen":"Maximize Screen",children:t?(0,s.jsx)(tG.A,{size:16}):(0,s.jsx)(tY.A,{size:16})})]}),(0,s.jsx)("div",{className:"flex-1 relative bg-black",children:r?(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center text-gray-400",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gray-600 rounded-full flex items-center justify-center",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,s.jsx)("path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"}),(0,s.jsx)("circle",{cx:"12",cy:"13",r:"3"})]})}),(0,s.jsx)("p",{className:"text-sm",children:"Live Camera Feed"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Robot Camera View"})]})}):(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center text-gray-400",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-red-600 rounded-full flex items-center justify-center",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,s.jsx)("path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"}),(0,s.jsx)("circle",{cx:"12",cy:"13",r:"3"}),(0,s.jsx)("line",{x1:"1",y1:"1",x2:"23",y2:"23"})]})}),(0,s.jsx)("p",{className:"text-sm",children:"Camera Disconnected"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Check robot connection"})]})})})]})}let tX=[{name:"Red",value:"#ef4444"},{name:"Blue",value:"#3b82f6"},{name:"Green",value:"#22c55e"},{name:"Yellow",value:"#eab308"},{name:"Orange",value:"#f97316"},{name:"Purple",value:"#a855f7"},{name:"Pink",value:"#ec4899"},{name:"Teal",value:"#14b8a6"},{name:"Indigo",value:"#6366f1"},{name:"Cyan",value:"#06b6d4"}];function tK(e){let{isOpen:t,onClose:a,onConfirm:r}=e,[n,o]=(0,i.useState)(""),[l,d]=(0,i.useState)(""),[c,m]=(0,i.useState)(tX[0].value),[h,g]=(0,i.useState)("#000000"),[p,b]=(0,i.useState)(!1),f=e=>{m(e),b(!1)};return(0,s.jsx)(eG,{open:t,onOpenChange:a,children:(0,s.jsxs)(eK,{className:"sm:max-w-md text-white border-[#0C6980]",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:[(0,s.jsx)(e$,{children:(0,s.jsx)(e0,{className:"text-xl font-semibold text-center text-white",children:"Enter Space Name"})}),(0,s.jsxs)("div",{className:"space-y-6 py-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(ti,{htmlFor:"pseudo",className:"text-sm font-medium text-white",children:"Pseudo"}),(0,s.jsx)(x,{id:"pseudo",placeholder:"Pseudo : Exp: 85",value:n,onChange:e=>o(e.target.value),className:"bg-[#0A3F4C] border-[#0C6980] text-white placeholder-gray-300 focus:border-white"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(ti,{htmlFor:"spaceName",className:"text-sm font-medium text-white",children:"Space Name"}),(0,s.jsx)(x,{id:"spaceName",placeholder:"Space Name",value:l,onChange:e=>d(e.target.value),className:"bg-[#0A3F4C] border-[#0C6980] text-white placeholder-gray-300 focus:border-white"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(ti,{className:"text-sm font-medium text-white",children:"Color"}),(0,s.jsx)("div",{className:"grid grid-cols-5 gap-2",children:tX.map(e=>(0,s.jsx)("button",{onClick:()=>f(e.value),className:"w-12 h-12 rounded-lg border-2 transition-all hover:scale-105 ".concat(c!==e.value||p?"border-[#0C6980] hover:border-white":"border-white shadow-lg"),style:{backgroundColor:e.value},title:e.name},e.value))}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("button",{onClick:()=>{b(!0)},className:"w-12 h-12 rounded-lg border-2 transition-all hover:scale-105 ".concat(p?"border-white shadow-lg":"border-[#0C6980] hover:border-white"),style:{backgroundColor:h},title:"Custom Color"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)(ti,{htmlFor:"customColor",className:"text-sm text-white",children:"Custom Color"}),(0,s.jsx)(x,{id:"customColor",type:"color",value:h,onChange:e=>{g(e.target.value),b(!0)},className:"w-full h-10 bg-[#0A3F4C] border-[#0C6980]"})]})]})]})]}),(0,s.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,s.jsx)(u,{variant:"outline",onClick:()=>{o(""),d(""),m(tX[0].value),g("#000000"),b(!1),a()},className:"flex-1 border-white text-black hover:bg-[#0C6980] hover:border-[#0C6980]",children:"Cancel"}),(0,s.jsx)(u,{onClick:()=>{n.trim()&&l.trim()&&(r({pseudo:n.trim(),spaceName:l.trim(),color:p?h:c}),o(""),d(""),m(tX[0].value),g("#000000"),b(!1))},disabled:!n.trim()||!l.trim(),className:"flex-1 text-white disabled:opacity-50 disabled:cursor-not-allowed",style:{background:"linear-gradient(90deg, #0C6980, #14b8a6)"},children:"Confirm"})]})]})})}let t$=["Floor 1","Floor 2","Floor 3","Basement","Ground Floor"],t0=["Robot-001","Robot-002","Robot-003","SteriBOT-Alpha","SteriBOT-Beta"],t1=["E. coli","Staphylococcus","Streptococcus","MRSA","C. difficile","All Types"];function t2(e){let{isOpen:t,onClose:a,onConfirm:r}=e,[n,o]=(0,i.useState)(""),[l,d]=(0,i.useState)(""),[c,m]=(0,i.useState)(""),[h,g]=(0,i.useState)(""),[p,b]=(0,i.useState)(""),f=()=>new Date().toISOString().split("T")[0];return(0,s.jsx)(eG,{open:t,onOpenChange:a,children:(0,s.jsxs)(eK,{className:"sm:max-w-lg text-white border-[#0C6980]",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:[(0,s.jsx)(e$,{children:(0,s.jsx)(e0,{className:"text-xl font-semibold text-center text-white",children:"Sterilization Configuration"})}),(0,s.jsxs)("div",{className:"space-y-6 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(ti,{htmlFor:"floor",className:"text-sm font-medium text-white",children:"Floor"}),(0,s.jsxs)(e3,{value:n,onValueChange:o,children:[(0,s.jsx)(e5,{className:"bg-[#0A3F4C] border-[#0C6980] text-white focus:border-white",children:(0,s.jsx)(e6,{placeholder:"Select floor"})}),(0,s.jsx)(te,{className:"border-[#0C6980]",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:t$.map(e=>(0,s.jsx)(tt,{value:e,className:"text-white hover:bg-[#0C6980] focus:bg-[#0C6980]",children:e},e))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(ti,{htmlFor:"robot",className:"text-sm font-medium text-white",children:"Robot"}),(0,s.jsxs)(e3,{value:l,onValueChange:d,children:[(0,s.jsx)(e5,{className:"bg-[#0A3F4C] border-[#0C6980] text-white focus:border-white",children:(0,s.jsx)(e6,{placeholder:"Select robot"})}),(0,s.jsx)(te,{className:"border-[#0C6980]",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:t0.map(e=>(0,s.jsx)(tt,{value:e,className:"text-white hover:bg-[#0C6980] focus:bg-[#0C6980]",children:e},e))})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(ti,{htmlFor:"date",className:"text-sm font-medium text-white",children:"Date"}),(0,s.jsx)(x,{id:"date",type:"date",value:c,onChange:e=>m(e.target.value),min:f(),className:"bg-[#0A3F4C] border-[#0C6980] text-white focus:border-white"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(ti,{htmlFor:"time",className:"text-sm font-medium text-white",children:"Time"}),(0,s.jsx)(x,{id:"time",type:"time",value:h,onChange:e=>g(e.target.value),min:c===f()?new Date().toTimeString().slice(0,5):void 0,className:"bg-[#0A3F4C] border-[#0C6980] text-white focus:border-white"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(ti,{htmlFor:"bacteriaType",className:"text-sm font-medium text-white",children:"Bacteria Types"}),(0,s.jsxs)(e3,{value:p,onValueChange:b,children:[(0,s.jsx)(e5,{className:"bg-[#0A3F4C] border-[#0C6980] text-white focus:border-white",children:(0,s.jsx)(e6,{placeholder:"Select bacteria type"})}),(0,s.jsx)(te,{className:"border-[#0C6980]",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:t1.map(e=>(0,s.jsx)(tt,{value:e,className:"text-white hover:bg-[#0C6980] focus:bg-[#0C6980]",children:e},e))})]})]})]}),(0,s.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,s.jsx)(u,{variant:"outline",onClick:()=>{o(""),d(""),m(""),g(""),b(""),a()},className:"flex-1 border-white text-black hover:bg-[#0C6980] hover:border-[#0C6980] hover:text-white",children:"Cancel"}),(0,s.jsx)(u,{onClick:()=>{n&&l&&c&&h&&p&&(r({floor:n,robot:l,date:c,time:h,bacteriaType:p}),o(""),d(""),m(""),g(""),b(""))},disabled:!n||!l||!c||!h||!p,className:"flex-1 text-white disabled:opacity-50 disabled:cursor-not-allowed",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:"Start Sterilization"})]})]})})}function t4(e){let{onClick:t}=e,a=es();return(0,s.jsxs)("button",{onClick:t,className:"w-full text-white py-2 px-4 rounded flex items-center justify-center gap-2 transition-all hover:scale-105",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,s.jsx)("path",{d:"M8 6l4-4 4 4"}),(0,s.jsx)("path",{d:"M12 2v10.3a4 4 0 0 1-1.172 2.872L4 22"}),(0,s.jsx)("path",{d:"m20 22-6.828-6.828A4 4 0 0 1 12 12.3"})]}),a.home.startSterilization]})}function t8(){let e=es(),[t,a]=(0,i.useState)(!1),[r,n]=(0,i.useState)(!1),[o,l]=(0,i.useState)(!1),[d,c]=(0,i.useState)(!1),[m,u]=(0,i.useState)(!1),[x,h]=(0,i.useState)(null),g=()=>{a(!1),n(!1),l(!1),h(null)},p=()=>{n(!r)};return(0,s.jsxs)("div",{className:"flex flex-col h-screen bg-teal-50 text-teal-800",children:[(0,s.jsx)("header",{className:"p-4 flex items-center gap-4 bg-white shadow-sm border-b border-teal-200",children:(0,s.jsx)("span",{className:"text-lg text-teal-800 font-semibold",children:e.home.configurationScanning})}),(0,s.jsx)("main",{className:"flex flex-1 overflow-hidden ",children:t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("section",{className:"flex-1 p-4 bg-white shadow-sm rounded-lg m-2 flex flex-col",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)(tW,{onMapClick:()=>{t&&c(!0)},isClickable:!0})}),o?(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(t4,{onClick:()=>{u(!0)}})}):(0,s.jsx)(tZ,{})]}),(0,s.jsxs)("section",{className:"flex-1 p-4 bg-white shadow-sm rounded-lg m-2 flex flex-col",children:[(0,s.jsx)(tQ,{isFullScreen:r,onToggleFullScreen:p}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsxs)("button",{onClick:g,className:"w-full text-white py-2 px-4 rounded flex items-center justify-center gap-2 transition-all hover:scale-105",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,s.jsx)("path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"}),(0,s.jsx)("path",{d:"M21 3v5h-5"}),(0,s.jsx)("path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"}),(0,s.jsx)("path",{d:"M8 16H3v5"})]}),e.home.reset]})})]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("section",{className:"flex-1 p-4 bg-white shadow-sm rounded-lg m-2 space-y-6 overflow-auto",children:[(0,s.jsx)(tU,{}),(0,s.jsx)(tJ,{}),(0,s.jsx)(t_,{onMapSaved:()=>{a(!0)},onReset:g,isMapSavedState:t})]}),(0,s.jsxs)("section",{className:"flex-1 p-4 bg-white shadow-sm rounded-lg m-2 flex flex-col",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)(tW,{})}),(0,s.jsx)(tZ,{})]})]})}),r&&(0,s.jsx)("div",{className:"fixed inset-0 z-50 bg-black",children:(0,s.jsx)(tQ,{isFullScreen:!0,onToggleFullScreen:p})}),(0,s.jsx)(tK,{isOpen:d,onClose:()=>c(!1),onConfirm:e=>{h(e),l(!0),c(!1)}}),(0,s.jsx)(t2,{isOpen:m,onClose:()=>u(!1),onConfirm:e=>{console.log("Starting sterilization with config:",e),u(!1)}})]})}var t3=a(8749),t6=a(2657),t5=a(6767);function t9(e){let{isOpen:t,onClose:a,onConnect:r}=e,n=es(),[o,l]=(0,i.useState)(null),[d,c]=(0,i.useState)(!1),m=async()=>{o&&(c(!0),setTimeout(()=>{r(o),c(!1)},2e3))};return t?(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,s.jsx)("div",{className:"relative z-10 w-full max-w-md",children:(0,s.jsxs)("div",{className:"network-modal-3d rounded-xl p-8 border border-teal-200/30 bg-teal-50/95 backdrop-blur-md shadow-3d modal-fade-in",children:[(0,s.jsx)("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent pointer-events-none"}),(0,s.jsx)("div",{className:"absolute inset-1 rounded-xl bg-gradient-to-tl from-teal-100/30 to-transparent pointer-events-none"}),(0,s.jsxs)("div",{className:"relative z-10",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-3 mb-4",children:[(0,s.jsxs)("div",{className:"w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-teal-600 to-teal-800 flex items-center justify-center shadow-lg",children:[(0,s.jsx)("img",{src:"/images/image.png",alt:"SteriBOT Logo",className:"w-10 h-10 object-cover rounded-full",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling.style.display="flex"}}),(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-teal-600 to-teal-800 rounded-full items-center justify-center text-white font-bold text-lg hidden",children:"S"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-teal-800",children:n.login.title}),(0,s.jsx)("p",{className:"text-teal-600 text-sm",children:n.login.subtitle})]})]}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-teal-800 mb-2",children:n.network.title}),(0,s.jsx)("p",{className:"text-teal-700 text-sm",children:n.network.subtitle})]}),(0,s.jsxs)("div",{className:"space-y-4 mb-8",children:[(0,s.jsx)("div",{onClick:()=>!d&&l("wifi"),className:"relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 shadow-lg ".concat("wifi"===o?"border-teal-600 bg-gradient-to-r from-teal-600 to-teal-700 text-white":"border-teal-300 bg-white/80 hover:border-teal-500 hover:bg-white/90"," ").concat(d?"cursor-not-allowed opacity-50":""),children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center transition-colors ".concat("wifi"===o?"bg-white/20":"bg-teal-100"),children:(0,s.jsx)(tg.A,{className:"w-6 h-6 ".concat("wifi"===o?"text-white":"text-teal-600")})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-medium ".concat("wifi"===o?"text-white":"text-teal-800"),children:n.network.wifi}),(0,s.jsx)("p",{className:"text-sm ".concat("wifi"===o?"text-teal-100":"text-teal-600"),children:n.network.wifiDescription})]}),"wifi"===o&&(0,s.jsx)("div",{className:"w-6 h-6 rounded-full bg-white flex items-center justify-center",children:(0,s.jsx)(e8.A,{className:"w-4 h-4 text-teal-600"})})]})}),(0,s.jsx)("div",{onClick:()=>!d&&l("mobile"),className:"relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 shadow-lg ".concat("mobile"===o?"border-teal-600 bg-gradient-to-r from-teal-600 to-teal-700 text-white":"border-teal-300 bg-white/80 hover:border-teal-500 hover:bg-white/90"," ").concat(d?"cursor-not-allowed opacity-50":""),children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center transition-colors ".concat("mobile"===o?"bg-white/20":"bg-teal-100"),children:(0,s.jsx)(t5.A,{className:"w-6 h-6 ".concat("mobile"===o?"text-white":"text-teal-600")})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-medium ".concat("mobile"===o?"text-white":"text-teal-800"),children:n.network.mobile}),(0,s.jsx)("p",{className:"text-sm ".concat("mobile"===o?"text-teal-100":"text-teal-600"),children:n.network.mobileDescription})]}),"mobile"===o&&(0,s.jsx)("div",{className:"w-6 h-6 rounded-full bg-white flex items-center justify-center",children:(0,s.jsx)(e8.A,{className:"w-4 h-4 text-teal-600"})})]})})]}),(0,s.jsx)("div",{className:"w-full",children:(0,s.jsx)(u,{onClick:m,disabled:!o||d,className:"w-full text-white py-3 px-4 disabled:opacity-50 disabled:cursor-not-allowed connect-button transition-all hover:scale-105 shadow-lg",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:d?(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),n.network.connecting]}):n.network.connect})})]})]})})}):null}var t7=a(9137),ae=a.n(t7),at=a(2138);function aa(e){let{connectionType:t,onConnectionComplete:a}=e,r=es(),[n,o]=(0,i.useState)(0),[l,d]=(0,i.useState)(0),c=[r.connecting.steps.initializing,r.connecting.steps.scanning,r.connecting.steps.establishing,r.connecting.steps.synchronizing,r.connecting.steps.established];return(0,i.useEffect)(()=>{let e=setInterval(()=>{o(t=>t>=100?(clearInterval(e),setTimeout(()=>a(),1e3),100):t+2)},100);return()=>clearInterval(e)},[a]),(0,i.useEffect)(()=>{let e=setInterval(()=>{d(t=>t>=c.length-1?(clearInterval(e),t):t+1)},1e3);return()=>clearInterval(e)},[c.length]),(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6 min-h-screen connecting-container-inverted flex flex-col",children:[(0,s.jsx)("div",{className:"jsx-18abe03fb973fdd6 absolute inset-0 connecting-background"}),(0,s.jsxs)("header",{className:"jsx-18abe03fb973fdd6 relative z-10 p-6 flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6 w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-teal-600 to-teal-800 flex items-center justify-center shadow-lg",children:[(0,s.jsx)("img",{src:"/images/image.png",alt:"SteriBOT Logo",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling.style.display="flex"},className:"jsx-18abe03fb973fdd6 w-10 h-10 object-cover rounded-full"}),(0,s.jsx)("div",{className:"jsx-18abe03fb973fdd6 w-10 h-10 bg-gradient-to-br from-teal-600 to-teal-800 rounded-full items-center justify-center text-white font-bold text-lg hidden",children:"S"})]}),(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6",children:[(0,s.jsx)("h1",{className:"jsx-18abe03fb973fdd6 text-2xl font-bold text-teal-800",children:r.connecting.title}),(0,s.jsx)("p",{className:"jsx-18abe03fb973fdd6 text-teal-600 text-sm",children:r.connecting.subtitle})]})]}),(0,s.jsx)("div",{className:"jsx-18abe03fb973fdd6 relative z-10 flex-1 flex items-center justify-center px-8",children:(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6 connecting-card-3d w-full max-w-4xl bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-teal-200/30 shadow-3d",children:[(0,s.jsx)("div",{className:"jsx-18abe03fb973fdd6 absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent pointer-events-none"}),(0,s.jsx)("div",{className:"jsx-18abe03fb973fdd6 absolute inset-1 rounded-2xl bg-gradient-to-tl from-teal-100/20 to-transparent pointer-events-none"}),(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6 relative z-10",children:[(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6 flex items-center justify-center gap-16 mb-16",children:[(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6 relative",children:[(0,s.jsx)("div",{className:"jsx-18abe03fb973fdd6 w-24 h-24 rounded-full bg-gradient-to-br from-teal-600/80 to-teal-800/80 backdrop-blur-sm flex items-center justify-center wifi-pulse border-2 border-teal-400/30 shadow-lg",children:"wifi"===t?(0,s.jsx)(tg.A,{className:"w-12 h-12 text-white"}):(0,s.jsx)(tb.A,{className:"w-12 h-12 text-white"})}),(0,s.jsx)("div",{className:"jsx-18abe03fb973fdd6 absolute -bottom-6 left-1/2 transform -translate-x-1/2",children:(0,s.jsx)("span",{className:"jsx-18abe03fb973fdd6 text-teal-800 text-sm font-medium",children:"wifi"===t?r.connecting.wifi:r.connecting.mobileData})})]}),(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6 flex items-center gap-4 flow-arrows",children:[(0,s.jsx)(at.A,{className:"w-8 h-8 text-teal-600"}),(0,s.jsx)(at.A,{className:"w-8 h-8 text-teal-700"}),(0,s.jsx)(at.A,{className:"w-8 h-8 text-teal-800"})]}),(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6 relative",children:[(0,s.jsx)("div",{className:"jsx-18abe03fb973fdd6 w-32 h-32 rounded-full bg-gradient-to-br from-teal-600/80 to-teal-800/80 backdrop-blur-sm flex items-center justify-center robot-waves border-2 border-teal-400/30 shadow-lg",children:(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6 w-20 h-20 rounded-lg bg-gradient-to-br from-white/20 to-white/5 flex items-center justify-center",children:[(0,s.jsx)("img",{src:"/images/image.png",alt:"SteriBOT Device",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling.style.display="flex"},className:"jsx-18abe03fb973fdd6 w-16 h-16 object-cover rounded-lg"}),(0,s.jsx)("div",{className:"jsx-18abe03fb973fdd6 w-16 h-16 bg-gradient-to-br from-teal-400 to-teal-600 rounded-lg items-center justify-center text-white font-bold text-2xl hidden",children:"S"})]})}),(0,s.jsx)("div",{className:"jsx-18abe03fb973fdd6 absolute -bottom-6 left-1/2 transform -translate-x-1/2",children:(0,s.jsx)("span",{className:"jsx-18abe03fb973fdd6 text-teal-800 text-sm font-medium",children:"SteriBOT"})})]})]}),(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6 text-center mb-8",children:[(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6 w-full max-w-md mx-auto mb-6",children:[(0,s.jsx)("div",{className:"jsx-18abe03fb973fdd6 w-full bg-teal-200/30 rounded-full h-3 backdrop-blur-sm border border-teal-300/40",children:(0,s.jsx)("div",{style:{width:"".concat(n,"%"),background:"linear-gradient(90deg, #14b8a6, #0C6980)"},className:"jsx-18abe03fb973fdd6 h-full rounded-full transition-all duration-300 ease-out"})}),(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6 mt-2 text-teal-800 text-sm font-medium",children:[n,r.connecting.complete]})]}),(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6 mb-4",children:[(0,s.jsx)("h2",{className:"jsx-18abe03fb973fdd6 text-xl font-semibold text-teal-800 mb-2",children:c[l]}),(0,s.jsx)("div",{className:"jsx-18abe03fb973fdd6 flex justify-center gap-2",children:c.map((e,t)=>(0,s.jsx)("div",{className:"jsx-18abe03fb973fdd6 "+"w-2 h-2 rounded-full transition-all duration-300 ".concat(t<=l?"bg-teal-600 scale-110":"bg-teal-300/50")},t))})]})]}),(0,s.jsxs)("div",{className:"jsx-18abe03fb973fdd6 text-center",children:[(0,s.jsx)("h3",{className:"jsx-18abe03fb973fdd6 text-2xl font-bold gradient-text mb-2",children:r.connecting.lookingFor}),(0,s.jsx)("p",{className:"jsx-18abe03fb973fdd6 text-teal-700 text-lg",children:r.connecting.ensureDevice})]})]})]})}),(0,s.jsx)("div",{className:"jsx-18abe03fb973fdd6 absolute inset-0 overflow-hidden pointer-events-none",children:[...Array(20)].map((e,t)=>(0,s.jsx)("div",{style:{left:"".concat(100*Math.random(),"%"),top:"".concat(100*Math.random(),"%"),animation:"float ".concat(3+4*Math.random(),"s ease-in-out infinite"),animationDelay:"".concat(2*Math.random(),"s")},className:"jsx-18abe03fb973fdd6 absolute w-1 h-1 bg-teal-400/30 rounded-full"},t))}),(0,s.jsx)(ae(),{id:"18abe03fb973fdd6",children:"@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg);opacity:.1}50%{-webkit-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg);opacity:.3}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg);opacity:.1}50%{-moz-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg);opacity:.3}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg);opacity:.1}50%{-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg);opacity:.3}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg);opacity:.1}50%{-webkit-transform:translatey(-20px)rotate(180deg);-moz-transform:translatey(-20px)rotate(180deg);-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg);opacity:.3}}"})]})}var as=a(7901);function ai(e){let{onStartScan:t}=e,a=es(),[r,n]=(0,i.useState)(!1);return(0,s.jsxs)("div",{className:"jsx-ecd2b0708a2ac8c5 min-h-screen robot-interface-container flex flex-col",children:[(0,s.jsxs)("header",{className:"jsx-ecd2b0708a2ac8c5 p-6 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"jsx-ecd2b0708a2ac8c5 flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"jsx-ecd2b0708a2ac8c5 w-12 h-12 rounded-full overflow-hidden bg-white/10 flex items-center justify-center",children:[(0,s.jsx)("img",{src:"/images/image.png",alt:"SteriBOT Logo",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling.style.display="flex"},className:"jsx-ecd2b0708a2ac8c5 w-10 h-10 object-cover rounded-full"}),(0,s.jsx)("div",{className:"jsx-ecd2b0708a2ac8c5 w-10 h-10 bg-white/20 rounded-full items-center justify-center text-white font-bold text-lg hidden",children:"S"})]}),(0,s.jsx)("div",{className:"jsx-ecd2b0708a2ac8c5",children:(0,s.jsx)("h1",{className:"jsx-ecd2b0708a2ac8c5 text-2xl font-bold text-white",children:a.robot.title})})]}),(0,s.jsxs)("div",{className:"jsx-ecd2b0708a2ac8c5 flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20",children:[(0,s.jsx)("div",{className:"jsx-ecd2b0708a2ac8c5 w-3 h-3 bg-green-400 rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"jsx-ecd2b0708a2ac8c5 text-white font-medium",children:a.robot.connected}),(0,s.jsx)(tg.A,{className:"w-4 h-4 text-white"})]})]}),(0,s.jsx)("div",{className:"jsx-ecd2b0708a2ac8c5 flex-1 flex flex-col items-center justify-center px-8",children:(0,s.jsxs)("div",{className:"jsx-ecd2b0708a2ac8c5 w-full max-w-2xl text-center",children:[(0,s.jsx)("div",{className:"jsx-ecd2b0708a2ac8c5 mb-8 flex justify-center",children:(0,s.jsxs)("div",{className:"jsx-ecd2b0708a2ac8c5 relative",children:[(0,s.jsxs)("div",{className:"jsx-ecd2b0708a2ac8c5 w-64 h-64 rounded-2xl bg-white/10 backdrop-blur-sm flex items-center justify-center robot-glow border-2 border-white/20",children:[(0,s.jsx)("img",{src:"/images/image.png",alt:"SteriBOT Robot",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling.style.display="flex"},className:"jsx-ecd2b0708a2ac8c5 w-48 h-48 object-cover rounded-xl"}),(0,s.jsx)("div",{className:"jsx-ecd2b0708a2ac8c5 w-48 h-48 bg-gradient-to-br from-white/20 to-white/5 rounded-xl items-center justify-center text-white font-bold text-6xl hidden",children:"S"})]}),(0,s.jsx)("div",{className:"jsx-ecd2b0708a2ac8c5 absolute inset-0 rounded-2xl border-2 border-teal-400/30 animate-ping"}),(0,s.jsx)("div",{className:"jsx-ecd2b0708a2ac8c5 absolute inset-2 rounded-2xl border border-teal-300/20 animate-pulse"})]})}),(0,s.jsxs)("div",{className:"jsx-ecd2b0708a2ac8c5 mb-8",children:[(0,s.jsx)("h2",{className:"jsx-ecd2b0708a2ac8c5 text-4xl font-bold gradient-text mb-3",children:"S4-CDZ12RR"}),(0,s.jsx)("p",{className:"jsx-ecd2b0708a2ac8c5 text-teal-200 text-xl font-medium",children:a.robot.subtitle})]}),(0,s.jsxs)("div",{className:"jsx-ecd2b0708a2ac8c5 flex items-center justify-center gap-8 mb-12",children:[(0,s.jsxs)("div",{className:"jsx-ecd2b0708a2ac8c5 flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20",children:[(0,s.jsx)(th.A,{className:"w-6 h-6 text-green-400"}),(0,s.jsx)("span",{className:"jsx-ecd2b0708a2ac8c5 text-white font-semibold text-lg",children:"82%"})]}),(0,s.jsxs)("div",{className:"jsx-ecd2b0708a2ac8c5 flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20",children:[(0,s.jsx)(tc.A,{className:"w-6 h-6 text-blue-400"}),(0,s.jsx)("span",{className:"jsx-ecd2b0708a2ac8c5 text-white font-semibold text-lg",children:"0.8m/s"})]}),(0,s.jsxs)("div",{className:"jsx-ecd2b0708a2ac8c5 flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20",children:[(0,s.jsx)("div",{className:"jsx-ecd2b0708a2ac8c5 w-3 h-3 bg-green-400 rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"jsx-ecd2b0708a2ac8c5 text-white font-semibold text-lg",children:a.robot.active})]})]}),(0,s.jsxs)("button",{onClick:t,onMouseEnter:()=>n(!0),onMouseLeave:()=>n(!1),className:"jsx-ecd2b0708a2ac8c5 group relative overflow-hidden bg-white/10 backdrop-blur-sm border-2 border-white/20 rounded-2xl px-12 py-6 text-white font-bold text-xl transition-all duration-300 hover:scale-105 hover:bg-white/20 start-scan-button",children:[(0,s.jsx)("div",{className:"jsx-ecd2b0708a2ac8c5 absolute inset-0 rounded-2xl bg-gradient-to-r from-teal-400 via-blue-500 to-teal-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-gradient-x"}),(0,s.jsx)("div",{className:"jsx-ecd2b0708a2ac8c5 absolute inset-[2px] rounded-2xl bg-gradient-to-br from-teal-600/80 to-blue-600/80 group-hover:from-teal-500/90 group-hover:to-blue-500/90 transition-all duration-300"}),(0,s.jsxs)("div",{className:"jsx-ecd2b0708a2ac8c5 relative flex items-center gap-4",children:[(0,s.jsx)(as.A,{className:"w-8 h-8 transition-transform duration-300 ".concat(r?"rotate-180":"")}),(0,s.jsx)("span",{className:"jsx-ecd2b0708a2ac8c5",children:a.robot.startScan})]}),(0,s.jsx)("div",{className:"jsx-ecd2b0708a2ac8c5 absolute inset-0 rounded-2xl bg-white/10 opacity-0 group-hover:opacity-100 group-hover:animate-ping"})]})]})}),(0,s.jsx)("div",{className:"jsx-ecd2b0708a2ac8c5 absolute inset-0 overflow-hidden pointer-events-none",children:[...Array(15)].map((e,t)=>(0,s.jsx)("div",{style:{left:"".concat(100*Math.random(),"%"),top:"".concat(100*Math.random(),"%"),animation:"float ".concat(4+3*Math.random(),"s ease-in-out infinite"),animationDelay:"".concat(2*Math.random(),"s")},className:"jsx-ecd2b0708a2ac8c5 absolute w-1 h-1 bg-white/20 rounded-full"},t))}),(0,s.jsx)(ae(),{id:"ecd2b0708a2ac8c5",children:"@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg);opacity:.1}50%{-webkit-transform:translatey(-30px)rotate(180deg);transform:translatey(-30px)rotate(180deg);opacity:.3}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg);opacity:.1}50%{-moz-transform:translatey(-30px)rotate(180deg);transform:translatey(-30px)rotate(180deg);opacity:.3}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg);opacity:.1}50%{-o-transform:translatey(-30px)rotate(180deg);transform:translatey(-30px)rotate(180deg);opacity:.3}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg);opacity:.1}50%{-webkit-transform:translatey(-30px)rotate(180deg);-moz-transform:translatey(-30px)rotate(180deg);-o-transform:translatey(-30px)rotate(180deg);transform:translatey(-30px)rotate(180deg);opacity:.3}}@-webkit-keyframes gradient-x{0%,100%{background-position:0%50%}50%{background-position:100%50%}}@-moz-keyframes gradient-x{0%,100%{background-position:0%50%}50%{background-position:100%50%}}@-o-keyframes gradient-x{0%,100%{background-position:0%50%}50%{background-position:100%50%}}@keyframes gradient-x{0%,100%{background-position:0%50%}50%{background-position:100%50%}}.robot-interface-container.jsx-ecd2b0708a2ac8c5{background:-webkit-linear-gradient(315deg,#0A3F4C 0%,#0C6980 50%,#14b8a6 100%);background:-moz-linear-gradient(315deg,#0A3F4C 0%,#0C6980 50%,#14b8a6 100%);background:-o-linear-gradient(315deg,#0A3F4C 0%,#0C6980 50%,#14b8a6 100%);background:linear-gradient(135deg,#0A3F4C 0%,#0C6980 50%,#14b8a6 100%);-webkit-background-size:400%400%;-moz-background-size:400%400%;-o-background-size:400%400%;background-size:400%400%;-webkit-animation:backgroundShift 8s ease infinite;-moz-animation:backgroundShift 8s ease infinite;-o-animation:backgroundShift 8s ease infinite;animation:backgroundShift 8s ease infinite}.robot-glow.jsx-ecd2b0708a2ac8c5{-webkit-box-shadow:0 0 30px rgba(20,184,166,.3);-moz-box-shadow:0 0 30px rgba(20,184,166,.3);box-shadow:0 0 30px rgba(20,184,166,.3)}.start-scan-button.jsx-ecd2b0708a2ac8c5{-webkit-box-shadow:0 10px 30px rgba(20,184,166,.2);-moz-box-shadow:0 10px 30px rgba(20,184,166,.2);box-shadow:0 10px 30px rgba(20,184,166,.2)}.start-scan-button.jsx-ecd2b0708a2ac8c5:hover{-webkit-box-shadow:0 15px 40px rgba(20,184,166,.4);-moz-box-shadow:0 15px 40px rgba(20,184,166,.4);box-shadow:0 15px 40px rgba(20,184,166,.4)}.animate-gradient-x.jsx-ecd2b0708a2ac8c5{-webkit-background-size:200%200%;-moz-background-size:200%200%;-o-background-size:200%200%;background-size:200%200%;-webkit-animation:gradient-x 2s ease infinite;-moz-animation:gradient-x 2s ease infinite;-o-animation:gradient-x 2s ease infinite;animation:gradient-x 2s ease infinite}@-webkit-keyframes backgroundShift{0%,100%{background-position:0%50%}50%{background-position:100%50%}}@-moz-keyframes backgroundShift{0%,100%{background-position:0%50%}50%{background-position:100%50%}}@-o-keyframes backgroundShift{0%,100%{background-position:0%50%}50%{background-position:100%50%}}@keyframes backgroundShift{0%,100%{background-position:0%50%}50%{background-position:100%50%}}"})]})}function ar(){let{language:e,setLanguage:t}=ea(),[a,r]=(0,i.useState)(!1),n=(0,i.useRef)(null),o=Q.find(t=>t.code===e)||Q[0];(0,i.useEffect)(()=>{function e(e){n.current&&!n.current.contains(e.target)&&r(!1)}return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let l=e=>{t(e),r(!1)};return(0,s.jsxs)("div",{ref:n,className:"jsx-7d2b4c98f0a8ae50 relative",children:[(0,s.jsx)("button",{onClick:()=>r(!a),"aria-label":"Select Language",className:"jsx-7d2b4c98f0a8ae50 language-selector-button w-12 h-12 rounded-full bg-white/10 backdrop-blur-md border-2 border-teal-300/30 flex items-center justify-center hover:bg-white/20 transition-all duration-300 shadow-lg group",children:(0,s.jsxs)("div",{className:"jsx-7d2b4c98f0a8ae50 relative flex items-center justify-center",children:[(0,s.jsx)("span",{className:"jsx-7d2b4c98f0a8ae50 text-2xl",children:o.flag}),(0,s.jsx)(e2.A,{className:"absolute -bottom-1 -right-1 w-3 h-3 text-teal-600 transition-transform duration-200 ".concat(a?"rotate-180":"")})]})}),a&&(0,s.jsxs)("div",{className:"jsx-7d2b4c98f0a8ae50 language-dropdown absolute top-14 right-0 w-64 bg-white/95 backdrop-blur-md rounded-xl border border-teal-200/30 shadow-3d overflow-hidden z-50",children:[(0,s.jsx)("div",{className:"jsx-7d2b4c98f0a8ae50 absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent pointer-events-none"}),(0,s.jsx)("div",{className:"jsx-7d2b4c98f0a8ae50 absolute inset-1 rounded-xl bg-gradient-to-tl from-teal-100/30 to-transparent pointer-events-none"}),(0,s.jsx)("div",{className:"jsx-7d2b4c98f0a8ae50 relative z-10 py-2",children:Q.map(t=>(0,s.jsxs)("button",{onClick:()=>l(t.code),className:"jsx-7d2b4c98f0a8ae50 "+"w-full px-4 py-3 flex items-center gap-3 hover:bg-teal-50/80 transition-all duration-200 ".concat(e===t.code?"bg-teal-100/50":""),children:[(0,s.jsx)("span",{className:"jsx-7d2b4c98f0a8ae50 text-xl",children:t.flag}),(0,s.jsxs)("div",{className:"jsx-7d2b4c98f0a8ae50 flex-1 text-left",children:[(0,s.jsx)("div",{className:"jsx-7d2b4c98f0a8ae50 text-teal-800 font-medium",children:t.name}),(0,s.jsx)("div",{className:"jsx-7d2b4c98f0a8ae50 text-teal-600 text-sm",children:t.nativeName})]}),e===t.code&&(0,s.jsx)(e8.A,{className:"w-5 h-5 text-teal-600"})]},t.code))})]}),(0,s.jsx)(ae(),{id:"7d2b4c98f0a8ae50",children:".language-selector-button.jsx-7d2b4c98f0a8ae50{-webkit-transform:perspective(1e3px)rotatex(2deg)rotatey(-2deg);-moz-transform:perspective(1e3px)rotatex(2deg)rotatey(-2deg);transform:perspective(1e3px)rotatex(2deg)rotatey(-2deg);-webkit-transition:-webkit-transform.3s ease,box-shadow.3s ease;-moz-transition:-moz-transform.3s ease,box-shadow.3s ease;-o-transition:-o-transform.3s ease,box-shadow.3s ease;transition:-webkit-transform.3s ease,box-shadow.3s ease;transition:-moz-transform.3s ease,box-shadow.3s ease;transition:-o-transform.3s ease,box-shadow.3s ease;transition:transform.3s ease,box-shadow.3s ease}.language-selector-button.jsx-7d2b4c98f0a8ae50:hover{-webkit-transform:perspective(1e3px)rotatex(0deg)rotatey(0deg)scale(1.05);-moz-transform:perspective(1e3px)rotatex(0deg)rotatey(0deg)scale(1.05);transform:perspective(1e3px)rotatex(0deg)rotatey(0deg)scale(1.05);-webkit-box-shadow:0 10px 25px rgba(20,184,166,.2);-moz-box-shadow:0 10px 25px rgba(20,184,166,.2);box-shadow:0 10px 25px rgba(20,184,166,.2)}.language-dropdown.jsx-7d2b4c98f0a8ae50{-webkit-transform:perspective(1e3px)rotatex(-5deg);-moz-transform:perspective(1e3px)rotatex(-5deg);transform:perspective(1e3px)rotatex(-5deg);-webkit-animation:dropdownSlide.3s ease-out forwards;-moz-animation:dropdownSlide.3s ease-out forwards;-o-animation:dropdownSlide.3s ease-out forwards;animation:dropdownSlide.3s ease-out forwards}@-webkit-keyframes dropdownSlide{from{opacity:0;-webkit-transform:perspective(1e3px)rotatex(-15deg)translatey(-10px);transform:perspective(1e3px)rotatex(-15deg)translatey(-10px)}to{opacity:1;-webkit-transform:perspective(1e3px)rotatex(0deg)translatey(0px);transform:perspective(1e3px)rotatex(0deg)translatey(0px)}}@-moz-keyframes dropdownSlide{from{opacity:0;-moz-transform:perspective(1e3px)rotatex(-15deg)translatey(-10px);transform:perspective(1e3px)rotatex(-15deg)translatey(-10px)}to{opacity:1;-moz-transform:perspective(1e3px)rotatex(0deg)translatey(0px);transform:perspective(1e3px)rotatex(0deg)translatey(0px)}}@-o-keyframes dropdownSlide{from{opacity:0;transform:perspective(1e3px)rotatex(-15deg)translatey(-10px)}to{opacity:1;transform:perspective(1e3px)rotatex(0deg)translatey(0px)}}@keyframes dropdownSlide{from{opacity:0;-webkit-transform:perspective(1e3px)rotatex(-15deg)translatey(-10px);-moz-transform:perspective(1e3px)rotatex(-15deg)translatey(-10px);transform:perspective(1e3px)rotatex(-15deg)translatey(-10px)}to{opacity:1;-webkit-transform:perspective(1e3px)rotatex(0deg)translatey(0px);-moz-transform:perspective(1e3px)rotatex(0deg)translatey(0px);transform:perspective(1e3px)rotatex(0deg)translatey(0px)}}"})]})}function an(e){let{onLogin:t}=e,a=es(),[r,n]=(0,i.useState)(""),[o,l]=(0,i.useState)(""),[d,c]=(0,i.useState)(!1),[m,h]=(0,i.useState)(!1),[g,p]=(0,i.useState)({email:"",password:""}),[b,f]=(0,i.useState)(!1),[v,j]=(0,i.useState)(!1),[y,N]=(0,i.useState)(!1),[w,S]=(0,i.useState)("wifi"),C=()=>{let e={email:"",password:""},t=!0;return r.trim()?/\S+@\S+\.\S+/.test(r)||(e.email=a.login.emailInvalid,t=!1):(e.email=a.login.emailRequired,t=!1),o.trim()?o.length<6&&(e.password=a.login.passwordMinLength,t=!1):(e.password=a.login.passwordRequired,t=!1),p(e),t},A=async e=>{e.preventDefault(),C()&&(h(!0),setTimeout(()=>{h(!1),f(!0)},1e3))};return y?(0,s.jsx)(ai,{onStartScan:()=>{N(!1),t(r,o)}}):v?(0,s.jsx)(aa,{connectionType:w,onConnectionComplete:()=>{j(!1),N(!0)}}):(0,s.jsxs)("div",{className:"min-h-screen login-container flex items-center justify-center p-4",children:[(0,s.jsx)("div",{className:"absolute inset-0 login-background"}),(0,s.jsx)("div",{className:"absolute top-6 right-6 z-20",children:(0,s.jsx)(ar,{})}),(0,s.jsx)("div",{className:"relative z-10 w-full max-w-md",children:(0,s.jsxs)("div",{className:"login-modal-3d rounded-xl p-8 border border-teal-200/30 bg-teal-50/95 backdrop-blur-md shadow-3d",children:[(0,s.jsx)("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent pointer-events-none"}),(0,s.jsxs)("div",{className:"relative z-10",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-3 mb-4",children:[(0,s.jsxs)("div",{className:"w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-teal-600 to-teal-800 flex items-center justify-center shadow-lg",children:[(0,s.jsx)("img",{src:"/images/image.png",alt:"SteriBOT Logo",className:"w-10 h-10 object-cover rounded-full",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling.style.display="flex"}}),(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-teal-600 to-teal-800 rounded-full items-center justify-center text-white font-bold text-lg hidden",children:"S"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-teal-800",children:a.login.title}),(0,s.jsx)("p",{className:"text-teal-600 text-sm",children:a.login.subtitle})]})]}),(0,s.jsx)("p",{className:"text-teal-700 text-sm",children:a.login.welcome})]}),(0,s.jsxs)("form",{onSubmit:A,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(ti,{htmlFor:"email",className:"text-sm font-medium text-teal-800",children:a.login.email}),(0,s.jsx)(x,{id:"email",type:"email",placeholder:a.login.emailPlaceholder,value:r,onChange:e=>{n(e.target.value),g.email&&p(e=>({...e,email:""}))},className:"bg-white/80 border-teal-300 text-teal-800 placeholder-teal-500 focus:border-teal-600 focus:ring-teal-200 shadow-inner ".concat(g.email?"border-red-400 focus:border-red-400":""),disabled:m}),g.email&&(0,s.jsx)("p",{className:"text-red-600 text-xs mt-1",children:g.email})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(ti,{htmlFor:"password",className:"text-sm font-medium text-teal-800",children:a.login.password}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(x,{id:"password",type:d?"text":"password",placeholder:a.login.passwordPlaceholder,value:o,onChange:e=>{l(e.target.value),g.password&&p(e=>({...e,password:""}))},className:"bg-white/80 border-teal-300 text-teal-800 placeholder-teal-500 focus:border-teal-600 focus:ring-teal-200 pr-10 shadow-inner ".concat(g.password?"border-red-400 focus:border-red-400":""),disabled:m}),(0,s.jsx)("button",{type:"button",onClick:()=>c(!d),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-teal-600 hover:text-teal-800 transition-colors",disabled:m,children:d?(0,s.jsx)(t3.A,{size:16}):(0,s.jsx)(t6.A,{size:16})})]}),g.password&&(0,s.jsx)("p",{className:"text-red-600 text-xs mt-1",children:g.password})]}),(0,s.jsx)(u,{type:"submit",disabled:m,className:"w-full text-white py-3 px-4 rounded-lg font-medium transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 connect-button shadow-lg",style:{background:"linear-gradient(90deg, #0A3F4C, #0C6980)"},children:m?(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),a.login.connecting]}):a.login.connect}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("button",{type:"button",onClick:()=>{console.log("Forgot password clicked")},className:"text-teal-600 hover:text-teal-800 text-sm transition-colors underline hover:no-underline",disabled:m,children:a.login.forgotPassword})})]})]})]})}),(0,s.jsx)(t9,{isOpen:b,onClose:()=>{f(!1)},onConnect:e=>{console.log("Connecting with:",e),S(e),f(!1),j(!0)}})]})}function ao(){let[e,t]=(0,i.useState)(!1),[a,r]=(0,i.useState)("/home");return e?(0,s.jsx)(et,{children:(0,s.jsxs)(z,{children:[(0,s.jsx)(er,{activeItem:a,onItemClick:r,onLogout:()=>{t(!1),r("/dashboard")}}),(0,s.jsx)(F,{children:(()=>{switch(a){case"/home":return(0,s.jsx)(t8,{});case"/dashboard":default:return(0,s.jsx)(eO,{});case"/profile":return(0,s.jsx)(e_,{});case"/robot-details":return(0,s.jsx)(tw,{});case"/robots-list":return(0,s.jsx)(tF,{});case"/sterilization-history":return(0,s.jsx)(tH,{});case"/obstacle-detection":return(0,s.jsx)(tV,{})}})()})]})}):(0,s.jsx)(et,{children:(0,s.jsx)(an,{onLogin:(e,a)=>{console.log("Login attempt:",{email:e,password:a}),t(!0)}})})}},3534:(e,t,a)=>{Promise.resolve().then(a.bind(a,1534))}},e=>{var t=t=>e(e.s=t);e.O(0,[106,441,684,358],()=>t(3534)),_N_E=e.O()}]);